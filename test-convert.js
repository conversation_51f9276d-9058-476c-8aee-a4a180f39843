const axios = require('axios');

async function testConvert() {
  try {
    console.log('测试转换功能...');
    
    // 模拟上传后的文件信息
    const files = [
      {
        originalName: '《公路桥涵施工技术规范》(JTGT3650—2020）_1.pdf',
        filename: '《公路桥涵施工技术规范》(JTGT3650—2020）_1.pdf',
        path: 'E:\\buildwise\\p-m\\pdf-converter-backend\\uploads\\《公路桥涵施工技术规范》(JTGT3650—2020）_1.pdf',
        size: 259010
      }
    ];
    
    // 开始转换
    const response = await axios.post('http://localhost:3001/api/convert', {
      files: files,
      options: {}
    });
    
    console.log('转换任务已创建:', response.data);
    
    const taskId = response.data.taskId;
    
    // 监控进度
    console.log('监控转换进度...');
    let completed = false;
    let attempts = 0;
    const maxAttempts = 30; // 最多等待60秒
    
    while (!completed && attempts < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, 2000));
      attempts++;
      
      try {
        const statusResponse = await axios.get(`http://localhost:3001/api/status/${taskId}`);
        const status = statusResponse.data;
        
        console.log(`进度: ${status.completed + status.failed}/${status.total}, 状态: ${status.status}`);
        
        if (status.status === 'completed' || status.status === 'failed') {
          completed = true;
          console.log('转换完成!');
          console.log('最终状态:', JSON.stringify(status, null, 2));
        }
      } catch (error) {
        console.error('获取状态失败:', error.message);
      }
    }
    
    if (!completed) {
      console.log('转换超时');
    }
    
  } catch (error) {
    console.error('转换失败:', error.message);
    if (error.response) {
      console.log('状态码:', error.response.status);
      console.log('响应:', error.response.data);
    }
  }
}

testConvert();
