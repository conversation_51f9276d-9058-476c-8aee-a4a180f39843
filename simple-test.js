const axios = require('axios');

async function simpleTest() {
  try {
    console.log('测试后端连接...');
    
    // 测试基本连接
    const response = await axios.get('http://localhost:3001');
    console.log('连接成功');
    
  } catch (error) {
    console.error('连接失败:', error.message);
    if (error.response) {
      console.log('状态码:', error.response.status);
      console.log('响应:', error.response.data);
    }
  }
}

simpleTest();
