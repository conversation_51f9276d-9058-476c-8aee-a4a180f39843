/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      // macOS风格的圆角
      borderRadius: {
        'macos': '12px',
        'macos-lg': '16px',
        'macos-xl': '20px',
        'macos-2xl': '24px',
      },
      // macOS风格的阴影
      boxShadow: {
        'macos': '0 4px 16px rgba(0, 0, 0, 0.1)',
        'macos-lg': '0 8px 32px rgba(0, 0, 0, 0.12)',
        'macos-xl': '0 16px 64px rgba(0, 0, 0, 0.15)',
        'macos-inner': 'inset 0 1px 0 rgba(255, 255, 255, 0.1)',
      },
      // macOS风格的背景模糊
      backdropBlur: {
        'macos': '20px',
        'macos-lg': '40px',
      },
      // macOS风格的颜色
      colors: {
        'macos': {
          'bg': 'rgba(255, 255, 255, 0.8)',
          'bg-dark': 'rgba(30, 30, 30, 0.8)',
          'surface': 'rgba(255, 255, 255, 0.6)',
          'surface-dark': 'rgba(45, 45, 45, 0.6)',
          'blue': '#007AFF',
          'blue-hover': '#0056CC',
          'gray': '#8E8E93',
          'gray-light': '#F2F2F7',
          'gray-dark': '#1C1C1E',
          'green': '#34C759',
          'red': '#FF3B30',
          'orange': '#FF9500',
        }
      },
      // macOS风格的渐变
      backgroundImage: {
        'macos-button': 'linear-gradient(180deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',
        'macos-button-hover': 'linear-gradient(180deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0.1) 100%)',
        'macos-blue': 'linear-gradient(180deg, #007AFF 0%, #0056CC 100%)',
        'macos-green': 'linear-gradient(180deg, #34C759 0%, #28A745 100%)',
      },
      // macOS风格的动画
      animation: {
        'macos-bounce': 'macos-bounce 0.3s ease-out',
        'macos-scale': 'macos-scale 0.2s ease-out',
      },
      keyframes: {
        'macos-bounce': {
          '0%': { transform: 'scale(1)' },
          '50%': { transform: 'scale(0.95)' },
          '100%': { transform: 'scale(1)' },
        },
        'macos-scale': {
          '0%': { transform: 'scale(0.95)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
      },
    },
  },
  plugins: [],
}
