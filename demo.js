const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

const API_BASE_URL = 'http://localhost:3001';

async function runDemo() {
  console.log('🚀 PDF批量转换工具演示');
  console.log('================================');
  
  try {
    // 1. 准备测试文件
    console.log('📁 准备测试文件...');
    const testFiles = [
      '《公路桥涵施工技术规范》(JTGT3650—2020）_1.pdf',
      '《公路桥涵施工技术规范》(JTGT3650—2020）_2.pdf',
      '《公路桥涵施工技术规范》(JTGT3650—2020）_3.pdf'
    ].filter(filename => {
      const filePath = path.join(__dirname, '《公路桥涵施工技术规范》(JTGT3650—2020）', filename);
      return fs.existsSync(filePath);
    });
    
    if (testFiles.length === 0) {
      console.log('❌ 未找到测试文件');
      return;
    }
    
    console.log(`✅ 找到 ${testFiles.length} 个测试文件`);
    
    // 2. 上传文件
    console.log('\n📤 上传文件...');
    const formData = new FormData();
    
    testFiles.forEach(filename => {
      const filePath = path.join(__dirname, '《公路桥涵施工技术规范》(JTGT3650—2020）', filename);
      formData.append('files', fs.createReadStream(filePath));
    });
    
    const uploadResponse = await axios.post(`${API_BASE_URL}/api/upload`, formData, {
      headers: formData.getHeaders(),
      timeout: 30000
    });
    
    console.log(`✅ 成功上传 ${uploadResponse.data.count} 个文件`);
    
    // 3. 开始转换
    console.log('\n🔄 开始批量转换...');
    const convertResponse = await axios.post(`${API_BASE_URL}/api/convert`, {
      files: uploadResponse.data.files,
      options: {}
    });
    
    const taskId = convertResponse.data.taskId;
    console.log(`✅ 转换任务已创建，任务ID: ${taskId}`);
    
    // 4. 监控进度
    console.log('\n📊 监控转换进度...');
    let completed = false;
    let lastProgress = -1;
    
    while (!completed) {
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const statusResponse = await axios.get(`${API_BASE_URL}/api/status/${taskId}`);
      const status = statusResponse.data;
      
      const currentProgress = status.completed + status.failed;
      if (currentProgress !== lastProgress) {
        console.log(`📈 进度: ${currentProgress}/${status.total} (成功: ${status.completed}, 失败: ${status.failed})`);
        lastProgress = currentProgress;
      }
      
      if (status.status === 'completed') {
        completed = true;
        console.log('\n🎉 转换完成!');
        console.log(`✅ 成功转换: ${status.completed} 个文件`);
        console.log(`❌ 转换失败: ${status.failed} 个文件`);
        
        if (status.errors && status.errors.length > 0) {
          console.log('\n❗ 错误信息:');
          status.errors.forEach(error => {
            console.log(`   - ${error.file}: ${error.error}`);
          });
        }
        
        // 5. 下载结果
        if (status.completed > 0) {
          console.log('\n💾 下载转换结果...');
          const downloadResponse = await axios.get(`${API_BASE_URL}/api/download/${taskId}`, {
            responseType: 'stream'
          });
          
          const outputPath = `demo-results-${taskId}.zip`;
          const writer = fs.createWriteStream(outputPath);
          downloadResponse.data.pipe(writer);
          
          await new Promise((resolve, reject) => {
            writer.on('finish', resolve);
            writer.on('error', reject);
          });
          
          console.log(`✅ 结果已保存到: ${outputPath}`);
        }
        
      } else if (status.status === 'failed') {
        completed = true;
        console.log('❌ 转换失败');
        console.log('错误信息:', status.error);
      }
    }
    
    console.log('\n🏁 演示完成!');
    console.log('================================');
    console.log('💡 提示:');
    console.log('   - 打开浏览器访问 http://localhost:5174 体验Web界面');
    console.log('   - 后端API运行在 http://localhost:3001');
    console.log('   - 查看生成的文件在 pdf-converter-backend/output/ 目录');
    
  } catch (error) {
    console.error('❌ 演示失败:', error.message);
    if (error.response) {
      console.log('状态码:', error.response.status);
      console.log('响应:', error.response.data);
    }
  }
}

// 运行演示
runDemo();
