const express = require('express');
const multer = require('multer');
const cors = require('cors');
const fs = require('fs-extra');
const path = require('path');
const { spawn } = require('child_process');
const http = require('http');
const socketIo = require('socket.io');
const archiver = require('archiver');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "http://localhost:5173",
    methods: ["GET", "POST"]
  }
});

const PORT = process.env.PORT || 3001;

// 中间件
app.use(cors());
app.use(express.json());

// 创建必要的目录
const uploadsDir = path.join(__dirname, 'uploads');
const outputDir = path.join(__dirname, 'output');
const tempDir = path.join(__dirname, 'temp');

fs.ensureDirSync(uploadsDir);
fs.ensureDirSync(outputDir);
fs.ensureDirSync(tempDir);

// 配置multer用于文件上传
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadsDir);
  },
  filename: (req, file, cb) => {
    // 保持原始文件名，支持中文
    cb(null, Buffer.from(file.originalname, 'latin1').toString('utf8'));
  }
});

const upload = multer({ 
  storage: storage,
  fileFilter: (req, file, cb) => {
    if (file.mimetype === 'application/pdf') {
      cb(null, true);
    } else {
      cb(new Error('只支持PDF文件'), false);
    }
  }
});

// 存储转换任务状态
const conversionTasks = new Map();

// Socket.IO连接处理
io.on('connection', (socket) => {
  console.log('客户端已连接:', socket.id);
  
  socket.on('disconnect', () => {
    console.log('客户端已断开连接:', socket.id);
  });
});

// 文件上传接口
app.post('/api/upload', upload.array('files'), (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({ error: '没有上传文件' });
    }

    const files = req.files.map(file => ({
      originalName: Buffer.from(file.originalname, 'latin1').toString('utf8'),
      filename: file.filename,
      path: file.path,
      size: file.size
    }));

    res.json({ 
      message: '文件上传成功', 
      files: files,
      count: files.length 
    });
  } catch (error) {
    console.error('文件上传错误:', error);
    res.status(500).json({ error: '文件上传失败' });
  }
});

// 开始批量转换接口
app.post('/api/convert', async (req, res) => {
  try {
    const { files, options = {} } = req.body;

    if (!files || files.length === 0) {
      return res.status(400).json({ error: '没有要转换的文件' });
    }

    const taskId = Date.now().toString();
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
    const resultFolderName = `conversion-${timestamp}-${taskId}`;
    const resultFolderPath = path.join(outputDir, resultFolderName);

    // 创建带时间戳的结果文件夹
    await fs.ensureDir(resultFolderPath);

    const task = {
      id: taskId,
      status: 'processing',
      total: files.length,
      completed: 0,
      failed: 0,
      results: [],
      errors: [],
      startTime: new Date(),
      resultFolderName: resultFolderName,
      resultFolderPath: resultFolderPath,
      isPaused: false,
      currentFileIndex: 0,
      filesStatus: files.map((file, index) => ({
        index: index,
        filename: file.originalName,
        status: 'waiting', // waiting, processing, completed, failed
        startTime: null,
        endTime: null,
        error: null
      })),
      processingSpeed: 0, // files per minute
      estimatedTimeRemaining: null
    };

    conversionTasks.set(taskId, task);

    // 异步处理转换任务
    processConversionTask(taskId, files, options);

    res.json({
      message: '转换任务已开始',
      taskId: taskId,
      total: files.length,
      resultFolderName: resultFolderName,
      resultFolderPath: resultFolderPath
    });
  } catch (error) {
    console.error('转换任务创建失败:', error);
    res.status(500).json({ error: '转换任务创建失败' });
  }
});

// 查询转换状态接口
app.get('/api/status/:taskId', (req, res) => {
  const { taskId } = req.params;
  const task = conversionTasks.get(taskId);
  
  if (!task) {
    return res.status(404).json({ error: '任务不存在' });
  }
  
  res.json(task);
});

// 下载转换结果接口
app.get('/api/download/:taskId', async (req, res) => {
  try {
    const { taskId } = req.params;
    const task = conversionTasks.get(taskId);

    if (!task) {
      return res.status(404).json({ error: '任务不存在' });
    }

    if (task.status !== 'completed') {
      return res.status(400).json({ error: '任务尚未完成' });
    }

    // 创建压缩包包含所有转换结果
    const archive = archiver('zip', { zlib: { level: 9 } });

    res.attachment(`${task.resultFolderName}.zip`);
    archive.pipe(res);

    // 添加整个结果文件夹到压缩包
    if (await fs.pathExists(task.resultFolderPath)) {
      archive.directory(task.resultFolderPath, task.resultFolderName);
    }

    archive.finalize();
  } catch (error) {
    console.error('下载失败:', error);
    res.status(500).json({ error: '下载失败' });
  }
});

// 打开结果文件夹接口
app.post('/api/open-folder/:taskId', async (req, res) => {
  try {
    const { taskId } = req.params;
    const task = conversionTasks.get(taskId);

    if (!task) {
      return res.status(404).json({ error: '任务不存在' });
    }

    if (!task.resultFolderPath || !await fs.pathExists(task.resultFolderPath)) {
      return res.status(404).json({ error: '结果文件夹不存在' });
    }

    // 根据操作系统打开文件夹
    const { spawn } = require('child_process');
    let command, args;

    switch (process.platform) {
      case 'win32':
        command = 'explorer';
        args = [task.resultFolderPath];
        break;
      case 'darwin':
        command = 'open';
        args = [task.resultFolderPath];
        break;
      case 'linux':
        command = 'xdg-open';
        args = [task.resultFolderPath];
        break;
      default:
        return res.status(400).json({ error: '不支持的操作系统' });
    }

    spawn(command, args, { detached: true, stdio: 'ignore' });

    res.json({
      message: '文件夹已打开',
      folderPath: task.resultFolderPath
    });
  } catch (error) {
    console.error('打开文件夹失败:', error);
    res.status(500).json({ error: '打开文件夹失败' });
  }
});

// 暂停转换任务
app.post('/api/pause/:taskId', (req, res) => {
  try {
    const { taskId } = req.params;
    const task = conversionTasks.get(taskId);

    if (!task) {
      return res.status(404).json({ error: '任务不存在' });
    }

    if (task.status !== 'processing') {
      return res.status(400).json({ error: '任务不在处理状态' });
    }

    task.isPaused = true;
    task.status = 'paused';

    io.emit('conversionPaused', { taskId });

    res.json({ message: '任务已暂停' });
  } catch (error) {
    console.error('暂停任务失败:', error);
    res.status(500).json({ error: '暂停任务失败' });
  }
});

// 恢复转换任务
app.post('/api/resume/:taskId', (req, res) => {
  try {
    const { taskId } = req.params;
    const task = conversionTasks.get(taskId);

    if (!task) {
      return res.status(404).json({ error: '任务不存在' });
    }

    if (task.status !== 'paused') {
      return res.status(400).json({ error: '任务不在暂停状态' });
    }

    task.isPaused = false;
    task.status = 'processing';

    io.emit('conversionResumed', { taskId });

    res.json({ message: '任务已恢复' });
  } catch (error) {
    console.error('恢复任务失败:', error);
    res.status(500).json({ error: '恢复任务失败' });
  }
});

// 处理转换任务的主要函数
async function processConversionTask(taskId, files, options) {
  const task = conversionTasks.get(taskId);
  
  try {
    // 按文件名排序，确保正确的页面顺序
    const sortedFiles = files.sort((a, b) => {
      const aNum = extractPageNumber(a.originalName);
      const bNum = extractPageNumber(b.originalName);
      return aNum - bNum;
    });

    for (let i = 0; i < sortedFiles.length; i++) {
      const file = sortedFiles[i];

      // 检查是否暂停
      while (task.isPaused) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      // 检查任务是否被取消
      if (task.status === 'cancelled') {
        break;
      }

      try {
        // 更新文件状态为处理中
        task.filesStatus[i].status = 'processing';
        task.filesStatus[i].startTime = new Date();
        task.currentFileIndex = i;

        // 计算处理速度和预计剩余时间
        const elapsedTime = (new Date() - task.startTime) / 1000 / 60; // 分钟
        if (elapsedTime > 0 && task.completed > 0) {
          task.processingSpeed = task.completed / elapsedTime;
          const remainingFiles = task.total - task.completed - task.failed;
          if (task.processingSpeed > 0) {
            task.estimatedTimeRemaining = remainingFiles / task.processingSpeed;
          }
        }

        // 更新任务状态
        io.emit('conversionProgress', {
          taskId,
          current: i + 1,
          total: sortedFiles.length,
          currentFile: file.originalName,
          status: 'processing',
          filesStatus: task.filesStatus,
          processingSpeed: task.processingSpeed,
          estimatedTimeRemaining: task.estimatedTimeRemaining
        });

        const result = await convertSingleFile(file, options, taskId);
        task.results.push(result);

        // 更新文件状态
        task.filesStatus[i].endTime = new Date();

        if (result.success) {
          task.completed++;
          task.filesStatus[i].status = 'completed';
        } else {
          task.failed++;
          task.filesStatus[i].status = 'failed';
          task.filesStatus[i].error = result.error;
          task.errors.push({
            file: file.originalName,
            error: result.error
          });
        }

      } catch (error) {
        console.error(`转换文件 ${file.originalName} 失败:`, error);
        task.failed++;
        task.filesStatus[i].status = 'failed';
        task.filesStatus[i].error = error.message;
        task.filesStatus[i].endTime = new Date();
        task.errors.push({
          file: file.originalName,
          error: error.message
        });
      }
    }

    // 合并所有成功转换的文件
    if (task.completed > 0) {
      await mergeConvertedFiles(taskId, task.results.filter(r => r.success));
    }

    task.status = 'completed';
    task.endTime = new Date();
    
    io.emit('conversionComplete', {
      taskId,
      completed: task.completed,
      failed: task.failed,
      errors: task.errors
    });
    
  } catch (error) {
    console.error('转换任务处理失败:', error);
    task.status = 'failed';
    task.error = error.message;
    
    io.emit('conversionError', {
      taskId,
      error: error.message
    });
  }
}

// 转换单个文件
async function convertSingleFile(file, options, taskId) {
  return new Promise(async (resolve) => {
    const inputPath = path.join(uploadsDir, file.filename);
    const outputBaseName = path.parse(file.originalName).name;
    const task = conversionTasks.get(taskId);

    try {
      // 创建临时输入目录（marker需要目录作为输入）
      const tempInputDir = path.join(tempDir, `input_${Date.now()}`);
      const tempOutputDir = path.join(tempDir, `output_${Date.now()}`);

      await fs.ensureDir(tempInputDir);
      await fs.ensureDir(tempOutputDir);

      // 复制文件到临时目录
      const tempInputFile = path.join(tempInputDir, file.filename);
      await fs.copy(inputPath, tempInputFile);

      // 使用marker转换
      const result = await convertWithMarker(tempInputDir, tempOutputDir, outputBaseName, task.resultFolderPath);

      // 清理临时目录
      await fs.remove(tempInputDir);
      await fs.remove(tempOutputDir);

      resolve(result);
    } catch (error) {
      resolve({
        success: false,
        filename: outputBaseName,
        originalName: file.originalName,
        error: error.message
      });
    }
  });
}

// 使用marker转换文件
function convertWithMarker(inputDir, tempOutputDir, outputBaseName, resultFolderPath) {
  return new Promise(async (resolve) => {
    // 使用marker命令转换PDF（先转换为markdown）
    const markerProcess = spawn('marker', [
      inputDir,
      '--output_dir', tempOutputDir,
      '--output_format', 'markdown'
    ]);

    let stderr = '';
    let stdout = '';

    markerProcess.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    markerProcess.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    markerProcess.on('close', async (code) => {
      if (code === 0) {
        try {
          // 查找生成的文件
          const outputSubDir = path.join(tempOutputDir, outputBaseName);
          const markdownFile = path.join(outputSubDir, `${outputBaseName}.md`);
          const jsonFile = path.join(outputSubDir, `${outputBaseName}_meta.json`);

          // 复制文件到时间戳结果文件夹
          const finalMarkdownPath = path.join(resultFolderPath, `${outputBaseName}.md`);
          const finalJsonPath = path.join(resultFolderPath, `${outputBaseName}.json`);

          let markdownExists = false;
          let jsonExists = false;

          if (await fs.pathExists(markdownFile)) {
            await fs.copy(markdownFile, finalMarkdownPath);
            markdownExists = true;
          }

          if (await fs.pathExists(jsonFile)) {
            await fs.copy(jsonFile, finalJsonPath);
            jsonExists = true;
          }

          if (markdownExists || jsonExists) {
            resolve({
              success: true,
              filename: outputBaseName,
              originalName: outputBaseName,
              markdownPath: markdownExists ? finalMarkdownPath : null,
              jsonPath: jsonExists ? finalJsonPath : null,
              stdout: stdout
            });
          } else {
            resolve({
              success: false,
              filename: outputBaseName,
              originalName: outputBaseName,
              error: '未找到生成的输出文件',
              stdout: stdout
            });
          }
        } catch (error) {
          resolve({
            success: false,
            filename: outputBaseName,
            originalName: outputBaseName,
            error: `文件处理错误: ${error.message}`,
            stdout: stdout
          });
        }
      } else {
        resolve({
          success: false,
          filename: outputBaseName,
          originalName: outputBaseName,
          error: stderr || `转换失败，退出码: ${code}`,
          stdout: stdout
        });
      }
    });

    markerProcess.on('error', (error) => {
      resolve({
        success: false,
        filename: outputBaseName,
        originalName: outputBaseName,
        error: error.message
      });
    });
  });
}

// 从文件名中提取页码
function extractPageNumber(filename) {
  const match = filename.match(/_(\d+)\.pdf$/i);
  return match ? parseInt(match[1], 10) : 0;
}

// 合并转换后的文件
async function mergeConvertedFiles(taskId, results) {
  try {
    const mergedMarkdownPath = path.join(outputDir, `merged-${taskId}.md`);
    const mergedJsonPath = path.join(outputDir, `merged-${taskId}.json`);
    
    let mergedMarkdown = '';
    let mergedJsonData = [];
    
    for (const result of results) {
      // 合并Markdown文件
      if (result.markdownPath && fs.existsSync(result.markdownPath)) {
        const content = await fs.readFile(result.markdownPath, 'utf8');
        mergedMarkdown += `\n\n<!-- 页面 ${extractPageNumber(result.originalName)} -->\n\n`;
        mergedMarkdown += content;
      }
      
      // 合并JSON文件
      if (result.jsonPath && fs.existsSync(result.jsonPath)) {
        try {
          const jsonContent = await fs.readFile(result.jsonPath, 'utf8');
          const jsonData = JSON.parse(jsonContent);
          mergedJsonData.push({
            page: extractPageNumber(result.originalName),
            filename: result.originalName,
            data: jsonData
          });
        } catch (error) {
          console.error(`解析JSON文件失败 ${result.jsonPath}:`, error);
        }
      }
    }
    
    // 保存合并后的文件
    if (mergedMarkdown) {
      await fs.writeFile(mergedMarkdownPath, mergedMarkdown, 'utf8');
    }
    
    if (mergedJsonData.length > 0) {
      await fs.writeFile(mergedJsonPath, JSON.stringify(mergedJsonData, null, 2), 'utf8');
    }
    
    // 更新任务结果
    const task = conversionTasks.get(taskId);
    if (task) {
      task.mergedFiles = {
        markdown: mergedMarkdownPath,
        json: mergedJsonPath
      };
    }
    
  } catch (error) {
    console.error('合并文件失败:', error);
  }
}

// 启动服务器
server.listen(PORT, () => {
  console.log(`PDF转换服务器运行在端口 ${PORT}`);
  console.log(`访问地址: http://localhost:${PORT}`);
});
