import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, File, X, FolderOpen } from 'lucide-react';

const FileUploader = ({ onFilesSelected, isUploading }) => {
  const [selectedFiles, setSelectedFiles] = useState([]);

  const onDrop = useCallback((acceptedFiles) => {
    // 过滤只保留PDF文件
    const pdfFiles = acceptedFiles.filter(file => 
      file.type === 'application/pdf' || file.name.toLowerCase().endsWith('.pdf')
    );
    
    setSelectedFiles(prev => [...prev, ...pdfFiles]);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf']
    },
    multiple: true,
    disabled: isUploading
  });

  const removeFile = (index) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const handleUpload = () => {
    if (selectedFiles.length > 0) {
      onFilesSelected(selectedFiles);
    }
  };

  const clearAll = () => {
    setSelectedFiles([]);
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="w-full">
      {/* 拖拽上传区域 */}
      <div
        {...getRootProps()}
        className={`
          border-2 border-dashed rounded-macos-lg p-12 text-center cursor-pointer transition-all duration-300
          ${isDragActive
            ? 'border-macos-blue bg-macos-blue/5 shadow-macos scale-[1.02]'
            : 'border-macos-gray/30 hover:border-macos-blue/50 hover:bg-macos-surface'
          }
          ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}
          backdrop-blur-sm
        `}
      >
        <input {...getInputProps()} />
        <div className="flex flex-col items-center space-y-6">
          {isDragActive ? (
            <>
              <div className="w-20 h-20 bg-macos-blue rounded-macos-lg flex items-center justify-center shadow-macos animate-macos-bounce">
                <FolderOpen className="w-10 h-10 text-white" />
              </div>
              <p className="text-xl font-semibold text-macos-blue">
                释放文件到这里...
              </p>
            </>
          ) : (
            <>
              <div className="w-20 h-20 bg-macos-surface rounded-macos-lg flex items-center justify-center shadow-macos border border-white/30">
                <Upload className="w-10 h-10 text-macos-gray" />
              </div>
              <div className="space-y-2">
                <p className="text-xl font-semibold text-gray-800">
                  拖拽PDF文件到这里，或点击选择文件
                </p>
                <p className="text-macos-gray">
                  支持批量上传多个PDF文件
                </p>
              </div>
            </>
          )}
        </div>
      </div>

      {/* 文件列表 */}
      {selectedFiles.length > 0 && (
        <div className="mt-8">
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-xl font-semibold text-gray-800">
              已选择文件 ({selectedFiles.length})
            </h3>
            <button
              onClick={clearAll}
              className="px-4 py-2 text-sm font-medium text-macos-red bg-macos-red/10 rounded-macos border border-macos-red/20 hover:bg-macos-red/20 transition-all duration-200 disabled:opacity-50"
              disabled={isUploading}
            >
              清空所有
            </button>
          </div>

          <div className="max-h-80 overflow-y-auto bg-macos-surface backdrop-blur-sm rounded-macos-lg border border-white/30 shadow-macos">
            {selectedFiles.map((file, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-4 border-b border-white/20 last:border-b-0 hover:bg-white/30 transition-colors duration-200"
              >
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-macos-red/10 rounded-macos flex items-center justify-center">
                    <File className="w-5 h-5 text-macos-red" />
                  </div>
                  <div>
                    <p className="text-sm font-semibold text-gray-800 truncate max-w-xs">
                      {file.name}
                    </p>
                    <p className="text-xs text-macos-gray">
                      {formatFileSize(file.size)}
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => removeFile(index)}
                  className="w-8 h-8 bg-macos-red/10 rounded-macos flex items-center justify-center text-macos-red hover:bg-macos-red/20 transition-all duration-200 disabled:opacity-50"
                  disabled={isUploading}
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 操作按钮 */}
      {selectedFiles.length > 0 && (
        <div className="mt-8 flex justify-center space-x-4">
          <button
            onClick={clearAll}
            className="px-8 py-3 bg-macos-surface backdrop-blur-sm border border-white/30 rounded-macos-lg text-gray-700 font-medium hover:bg-white/50 transition-all duration-200 shadow-macos disabled:opacity-50"
            disabled={isUploading}
          >
            清空
          </button>
          <button
            onClick={handleUpload}
            className={`
              px-8 py-3 rounded-macos-lg font-semibold transition-all duration-200 shadow-macos
              ${isUploading
                ? 'bg-macos-gray text-white cursor-not-allowed'
                : 'bg-macos-blue text-white hover:bg-macos-blue-hover hover:shadow-macos-lg hover:scale-105 active:scale-95'
              }
            `}
            disabled={isUploading || selectedFiles.length === 0}
          >
            {isUploading ? '上传中...' : `开始转换 (${selectedFiles.length} 个文件)`}
          </button>
        </div>
      )}

      {/* 使用说明 */}
      <div className="mt-10 p-6 bg-macos-surface backdrop-blur-sm rounded-macos-lg border border-white/30 shadow-macos">
        <h4 className="font-semibold text-gray-800 mb-4 text-lg">使用说明：</h4>
        <ul className="text-macos-gray space-y-3 leading-relaxed">
          <li className="flex items-start space-x-2">
            <span className="w-1.5 h-1.5 bg-macos-blue rounded-full mt-2 flex-shrink-0"></span>
            <span>支持拖拽整个文件夹或批量选择PDF文件</span>
          </li>
          <li className="flex items-start space-x-2">
            <span className="w-1.5 h-1.5 bg-macos-blue rounded-full mt-2 flex-shrink-0"></span>
            <span>系统会自动按文件名中的页码排序（如：文档_1.pdf, 文档_2.pdf）</span>
          </li>
          <li className="flex items-start space-x-2">
            <span className="w-1.5 h-1.5 bg-macos-blue rounded-full mt-2 flex-shrink-0"></span>
            <span>转换完成后会生成单独的Markdown和JSON文件，以及合并后的完整文档</span>
          </li>
          <li className="flex items-start space-x-2">
            <span className="w-1.5 h-1.5 bg-macos-blue rounded-full mt-2 flex-shrink-0"></span>
            <span>支持中文文件名和路径</span>
          </li>
        </ul>
      </div>
    </div>
  );
};

export default FileUploader;
