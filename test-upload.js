const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

async function testUpload() {
  try {
    console.log('测试文件上传...');
    
    // 测试文件路径
    const testFile = path.join(__dirname, '《公路桥涵施工技术规范》(JTGT3650—2020）', '《公路桥涵施工技术规范》(JTGT3650—2020）_1.pdf');
    
    if (!fs.existsSync(testFile)) {
      console.error('测试文件不存在:', testFile);
      return;
    }
    
    console.log('文件存在，开始上传...');
    
    // 创建FormData
    const formData = new FormData();
    formData.append('files', fs.createReadStream(testFile));
    
    // 上传文件
    const response = await axios.post('http://localhost:3001/api/upload', formData, {
      headers: {
        ...formData.getHeaders(),
      },
      timeout: 30000
    });
    
    console.log('上传成功:', response.data);
    
  } catch (error) {
    console.error('上传失败:', error.message);
    if (error.response) {
      console.log('状态码:', error.response.status);
      console.log('响应:', error.response.data);
    }
  }
}

testUpload();
