import React, { useState } from 'react';
import { CheckCircle, XCircle, Clock, Download, AlertCircle, FolderOpen, <PERSON><PERSON>, Pause, Play, Timer, Zap } from 'lucide-react';

const ConversionProgress = ({
  taskStatus,
  progress,
  onDownload,
  onReset,
  onOpenFolder,
  onPause,
  onResume
}) => {
  const [showFileDetails, setShowFileDetails] = useState(false);

  if (!taskStatus) return null;

  const {
    status,
    total,
    completed,
    failed,
    errors,
    currentFile,
    resultFolderPath,
    resultFolderName,
    filesStatus = [],
    processingSpeed = 0,
    estimatedTimeRemaining = null
  } = taskStatus;

  const progressPercentage = total > 0 ? Math.round(((completed + failed) / total) * 100) : 0;

  // 复制路径到剪贴板
  const copyPath = async () => {
    if (resultFolderPath) {
      try {
        await navigator.clipboard.writeText(resultFolderPath);
        // 可以添加一个提示消息
      } catch (err) {
        console.error('复制失败:', err);
      }
    }
  };

  // 格式化时间
  const formatTime = (minutes) => {
    if (!minutes || minutes === Infinity) return '--';
    if (minutes < 1) return '< 1分钟';
    if (minutes < 60) return `${Math.round(minutes)}分钟`;
    const hours = Math.floor(minutes / 60);
    const mins = Math.round(minutes % 60);
    return `${hours}小时${mins}分钟`;
  };

  // 格式化速度
  const formatSpeed = (speed) => {
    if (!speed || speed === 0) return '--';
    return `${speed.toFixed(1)} 文件/分钟`;
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'processing':
        return <Clock className="w-6 h-6 text-macos-blue animate-spin" />;
      case 'paused':
        return <Pause className="w-6 h-6 text-macos-orange" />;
      case 'completed':
        return <CheckCircle className="w-6 h-6 text-macos-green" />;
      case 'failed':
        return <XCircle className="w-6 h-6 text-macos-red" />;
      default:
        return <Clock className="w-6 h-6 text-macos-gray" />;
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'processing':
        return '转换中...';
      case 'paused':
        return '已暂停';
      case 'completed':
        return '转换完成';
      case 'failed':
        return '转换失败';
      default:
        return '准备中...';
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'processing':
        return 'text-macos-blue';
      case 'paused':
        return 'text-macos-orange';
      case 'completed':
        return 'text-macos-green';
      case 'failed':
        return 'text-macos-red';
      default:
        return 'text-macos-gray';
    }
  };

  return (
    <div className="w-full">
      <div className="space-y-8">
        {/* 状态标题 */}
        <div className="flex items-center justify-center space-x-4">
          <div className="w-12 h-12 bg-macos-surface rounded-macos-lg flex items-center justify-center shadow-macos border border-white/30">
            {getStatusIcon()}
          </div>
          <h2 className={`text-2xl font-bold ${getStatusColor()} tracking-tight`}>
            {getStatusText()}
          </h2>
        </div>

        {/* 进度条和详细信息 */}
        <div className="bg-macos-surface backdrop-blur-sm rounded-macos-lg p-6 border border-white/30 shadow-macos">
          <div className="flex justify-between text-sm text-macos-gray mb-4 font-medium">
            <span>总进度</span>
            <span>{completed + failed} / {total} ({progressPercentage}%)</span>
          </div>
          <div className="w-full bg-macos-gray-light rounded-full h-4 shadow-macos-inner mb-6">
            <div
              className="bg-gradient-to-r from-macos-blue to-macos-blue-hover h-4 rounded-full transition-all duration-500 ease-out shadow-macos"
              style={{ width: `${progressPercentage}%` }}
            />
          </div>

          {/* 详细进度信息 */}
          <div className="grid grid-cols-2 gap-6">
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-macos-blue/20 rounded-macos flex items-center justify-center">
                  <Zap className="w-4 h-4 text-macos-blue" />
                </div>
                <div>
                  <p className="text-sm text-macos-gray">处理速度</p>
                  <p className="font-semibold text-gray-800">{formatSpeed(processingSpeed)}</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-macos-orange/20 rounded-macos flex items-center justify-center">
                  <Timer className="w-4 h-4 text-macos-orange" />
                </div>
                <div>
                  <p className="text-sm text-macos-gray">预计剩余时间</p>
                  <p className="font-semibold text-gray-800">{formatTime(estimatedTimeRemaining)}</p>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-macos-green/20 rounded-macos flex items-center justify-center">
                  <CheckCircle className="w-4 h-4 text-macos-green" />
                </div>
                <div>
                  <p className="text-sm text-macos-gray">成功完成</p>
                  <p className="font-semibold text-gray-800">{completed} 个文件</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-macos-red/20 rounded-macos flex items-center justify-center">
                  <XCircle className="w-4 h-4 text-macos-red" />
                </div>
                <div>
                  <p className="text-sm text-macos-gray">转换失败</p>
                  <p className="font-semibold text-gray-800">{failed} 个文件</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 当前处理文件 */}
        {(status === 'processing' || status === 'paused') && currentFile && (
          <div className="bg-macos-blue/10 backdrop-blur-sm rounded-macos-lg p-6 border border-macos-blue/20 shadow-macos">
            <div className="flex items-center justify-between">
              <p className="text-macos-blue font-medium">
                <span className="font-semibold">
                  {status === 'paused' ? '已暂停：' : '正在处理：'}
                </span>
                <span className="ml-2">{currentFile}</span>
              </p>

              {/* 暂停/恢复按钮 */}
              <div className="flex space-x-2">
                {status === 'processing' && (
                  <button
                    onClick={onPause}
                    className="flex items-center space-x-2 px-4 py-2 bg-macos-orange/20 text-macos-orange rounded-macos border border-macos-orange/30 hover:bg-macos-orange/30 transition-colors"
                  >
                    <Pause className="w-4 h-4" />
                    <span>暂停</span>
                  </button>
                )}

                {status === 'paused' && (
                  <button
                    onClick={onResume}
                    className="flex items-center space-x-2 px-4 py-2 bg-macos-green/20 text-macos-green rounded-macos border border-macos-green/30 hover:bg-macos-green/30 transition-colors"
                  >
                    <Play className="w-4 h-4" />
                    <span>恢复</span>
                  </button>
                )}
              </div>
            </div>
          </div>
        )}

        {/* 文件状态列表 */}
        {filesStatus.length > 0 && (
          <div className="bg-macos-surface backdrop-blur-sm rounded-macos-lg border border-white/30 shadow-macos">
            <div className="p-6 border-b border-white/20">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold text-gray-800 text-lg">文件处理状态</h3>
                <button
                  onClick={() => setShowFileDetails(!showFileDetails)}
                  className="text-sm text-macos-blue hover:text-macos-blue-hover transition-colors"
                >
                  {showFileDetails ? '隐藏详情' : '显示详情'}
                </button>
              </div>
            </div>

            {showFileDetails && (
              <div className="max-h-64 overflow-y-auto">
                {filesStatus.map((fileStatus, index) => (
                  <div key={index} className="p-4 border-b border-white/10 last:border-b-0">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className={`w-3 h-3 rounded-full ${
                          fileStatus.status === 'completed' ? 'bg-macos-green' :
                          fileStatus.status === 'failed' ? 'bg-macos-red' :
                          fileStatus.status === 'processing' ? 'bg-macos-blue animate-pulse' :
                          'bg-macos-gray'
                        }`} />
                        <span className="text-sm font-medium text-gray-800 truncate max-w-xs">
                          {fileStatus.filename}
                        </span>
                      </div>
                      <div className="text-xs text-macos-gray">
                        {fileStatus.status === 'waiting' && '等待中'}
                        {fileStatus.status === 'processing' && '处理中...'}
                        {fileStatus.status === 'completed' && '已完成'}
                        {fileStatus.status === 'failed' && '失败'}
                      </div>
                    </div>
                    {fileStatus.error && (
                      <p className="text-xs text-macos-red mt-2 ml-6">
                        错误: {fileStatus.error}
                      </p>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* 统计信息 */}
        <div className="grid grid-cols-3 gap-6">
          <div className="text-center p-6 bg-macos-surface backdrop-blur-sm rounded-macos-lg border border-white/30 shadow-macos">
            <div className="text-3xl font-bold text-gray-800 mb-2">{total}</div>
            <div className="text-sm text-macos-gray font-medium">总文件数</div>
          </div>
          <div className="text-center p-6 bg-macos-green/10 backdrop-blur-sm rounded-macos-lg border border-macos-green/20 shadow-macos">
            <div className="text-3xl font-bold text-macos-green mb-2">{completed}</div>
            <div className="text-sm text-macos-green font-medium">成功转换</div>
          </div>
          <div className="text-center p-6 bg-macos-red/10 backdrop-blur-sm rounded-macos-lg border border-macos-red/20 shadow-macos">
            <div className="text-3xl font-bold text-macos-red mb-2">{failed}</div>
            <div className="text-sm text-macos-red font-medium">转换失败</div>
          </div>
        </div>

        {/* 错误列表 */}
        {errors && errors.length > 0 && (
          <div className="bg-macos-red/10 backdrop-blur-sm rounded-macos-lg p-6 border border-macos-red/20 shadow-macos">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-8 h-8 bg-macos-red/20 rounded-macos flex items-center justify-center">
                <AlertCircle className="w-5 h-5 text-macos-red" />
              </div>
              <h3 className="font-semibold text-macos-red text-lg">转换错误</h3>
            </div>
            <div className="max-h-40 overflow-y-auto space-y-3">
              {errors.map((error, index) => (
                <div key={index} className="text-sm text-macos-red bg-white/30 rounded-macos p-3">
                  <span className="font-semibold">{error.file}:</span>
                  <span className="ml-2">{error.error}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 结果文件夹信息 */}
        {(status === 'completed' || status === 'processing') && resultFolderPath && (
          <div className="bg-macos-surface backdrop-blur-sm rounded-macos-lg p-6 border border-white/30 shadow-macos">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-8 h-8 bg-macos-blue/20 rounded-macos flex items-center justify-center">
                <FolderOpen className="w-5 h-5 text-macos-blue" />
              </div>
              <h3 className="font-semibold text-gray-800 text-lg">结果文件夹</h3>
            </div>
            <div className="space-y-3">
              <div>
                <p className="text-sm text-macos-gray mb-1">文件夹名称:</p>
                <p className="font-mono text-sm bg-white/50 rounded-macos px-3 py-2 border border-white/30">
                  {resultFolderName}
                </p>
              </div>
              <div>
                <p className="text-sm text-macos-gray mb-1">完整路径:</p>
                <div className="flex items-center space-x-2">
                  <p className="font-mono text-sm bg-white/50 rounded-macos px-3 py-2 border border-white/30 flex-1 truncate">
                    {resultFolderPath}
                  </p>
                  <button
                    onClick={copyPath}
                    className="w-8 h-8 bg-macos-blue/10 rounded-macos flex items-center justify-center text-macos-blue hover:bg-macos-blue/20 transition-colors"
                    title="复制路径"
                  >
                    <Copy className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex justify-center space-x-4 pt-4">
          {status === 'completed' && completed > 0 && (
            <>
              <button
                onClick={onDownload}
                className="flex items-center space-x-3 px-8 py-4 bg-macos-green text-white rounded-macos-lg font-semibold shadow-macos hover:bg-macos-green/90 hover:shadow-macos-lg hover:scale-105 active:scale-95 transition-all duration-200"
              >
                <Download className="w-5 h-5" />
                <span>下载压缩包</span>
              </button>
              <button
                onClick={onOpenFolder}
                className="flex items-center space-x-3 px-8 py-4 bg-macos-blue text-white rounded-macos-lg font-semibold shadow-macos hover:bg-macos-blue-hover hover:shadow-macos-lg hover:scale-105 active:scale-95 transition-all duration-200"
              >
                <FolderOpen className="w-5 h-5" />
                <span>打开文件夹</span>
              </button>
            </>
          )}

          {(status === 'completed' || status === 'failed') && (
            <button
              onClick={onReset}
              className="px-8 py-4 bg-macos-surface backdrop-blur-sm border border-white/30 text-gray-700 rounded-macos-lg font-semibold shadow-macos hover:bg-white/50 hover:shadow-macos-lg hover:scale-105 active:scale-95 transition-all duration-200"
            >
              重新开始
            </button>
          )}
        </div>

        {/* 转换完成提示 */}
        {status === 'completed' && (
          <div className="bg-macos-green/10 backdrop-blur-sm rounded-macos-lg p-6 border border-macos-green/20 shadow-macos">
            <div className="flex items-start space-x-4">
              <div className="w-10 h-10 bg-macos-green/20 rounded-macos-lg flex items-center justify-center flex-shrink-0">
                <CheckCircle className="w-6 h-6 text-macos-green" />
              </div>
              <div className="text-macos-green">
                <p className="font-bold text-lg mb-3">转换完成！</p>
                <ul className="space-y-2 text-sm leading-relaxed">
                  <li className="flex items-start space-x-2">
                    <span className="w-1.5 h-1.5 bg-macos-green rounded-full mt-2 flex-shrink-0"></span>
                    <span>已生成 {completed} 个文件的Markdown和JSON格式</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="w-1.5 h-1.5 bg-macos-green rounded-full mt-2 flex-shrink-0"></span>
                    <span>已创建合并后的完整文档</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="w-1.5 h-1.5 bg-macos-green rounded-full mt-2 flex-shrink-0"></span>
                    <span>点击"下载转换结果"获取所有文件的压缩包</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ConversionProgress;
