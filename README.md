# PDF批量转换工具

一个基于Web的PDF批量转换工具，使用Marker引擎将PDF文档转换为Markdown和JSON格式。

## 功能特性

- 🚀 **批量转换**: 支持同时处理多个PDF文件
- 📁 **拖拽上传**: 支持文件夹拖拽和批量文件选择
- 📄 **多格式输出**: 生成Markdown和JSON两种格式
- 🔄 **实时进度**: 实时显示转换进度和状态
- 📊 **错误处理**: 完善的错误处理和日志记录
- 🔗 **文件合并**: 自动按页码顺序合并分页PDF文件
- 💾 **批量下载**: 一键下载所有转换结果的压缩包
- 🌐 **中文支持**: 完全支持中文文件名和路径

## 技术架构

### 前端
- **React 18** - 现代化用户界面
- **Vite** - 快速开发构建工具
- **Tailwind CSS** - 响应式样式框架
- **React Dropzone** - 文件拖拽上传
- **Axios** - HTTP客户端
- **Socket.IO Client** - 实时通信

### 后端
- **Node.js** - 服务器运行环境
- **Express** - Web应用框架
- **Socket.IO** - 实时双向通信
- **Multer** - 文件上传处理
- **Marker** - PDF转换引擎
- **Archiver** - 文件压缩

## 安装和运行

### 前置要求

1. **Node.js** (版本 16 或更高)
2. **Python 3.8+** 
3. **Marker工具** 及其依赖

### 安装步骤

1. **安装Marker工具**:
   ```bash
   pip install marker-pdf
   pip install torch torchvision --index-url https://download.pytorch.org/whl/cpu
   ```

2. **安装项目依赖**:
   ```bash
   # 后端依赖
   cd pdf-converter-backend
   npm install
   
   # 前端依赖
   cd ../pdf-converter
   npm install
   ```

### 启动应用

1. **启动后端服务器**:
   ```bash
   cd pdf-converter-backend
   npm start
   ```
   服务器将运行在 http://localhost:3001

2. **启动前端应用**:
   ```bash
   cd pdf-converter
   npm run dev
   ```
   应用将运行在 http://localhost:5174

## 使用说明

### 基本使用流程

1. **打开应用**: 在浏览器中访问 http://localhost:5174
2. **上传文件**: 
   - 拖拽PDF文件或文件夹到上传区域
   - 或点击选择文件按钮
3. **开始转换**: 点击"开始转换"按钮
4. **监控进度**: 实时查看转换进度和状态
5. **下载结果**: 转换完成后下载压缩包

### 文件命名规范

为了正确处理分页PDF文件，建议使用以下命名格式：
```
文档名称_1.pdf
文档名称_2.pdf
文档名称_3.pdf
...
```

系统会自动按页码顺序合并这些文件。

### API接口

#### 文件上传
```http
POST /api/upload
Content-Type: multipart/form-data

files: PDF文件(支持多文件)
```

#### 开始转换
```http
POST /api/convert
Content-Type: application/json

{
  "files": [文件信息数组],
  "options": {}
}
```

#### 查询状态
```http
GET /api/status/:taskId
```

#### 下载结果
```http
GET /api/download/:taskId
```

## 输出格式

### Markdown文件
- 保持原文档的结构和格式
- 正确提取标题、段落、列表等元素
- 保留图片位置信息

### JSON文件
- 包含文档的结构化数据
- 元数据信息
- 页面级别的详细信息

## 错误处理

应用包含完善的错误处理机制：

- **文件格式验证**: 只接受PDF文件
- **转换错误**: 跳过有问题的文件，继续处理其他文件
- **网络错误**: 自动重试和错误提示
- **进度恢复**: 支持转换过程中的状态恢复

## 性能优化

- **并发处理**: 支持多文件并发转换
- **内存管理**: 自动清理临时文件
- **进度缓存**: 实时更新转换状态
- **文件压缩**: 高效的结果打包

## 故障排除

### 常见问题

1. **Marker工具未找到**:
   ```bash
   pip install marker-pdf
   ```

2. **PyTorch依赖问题**:
   ```bash
   pip install torch torchvision --index-url https://download.pytorch.org/whl/cpu
   ```

3. **端口占用**:
   - 后端默认端口: 3001
   - 前端默认端口: 5174
   - 可在配置文件中修改

4. **中文文件名问题**:
   - 确保系统支持UTF-8编码
   - Windows用户可能需要设置系统区域

## 开发说明

### 项目结构
```
├── pdf-converter/          # 前端React应用
│   ├── src/
│   │   ├── components/     # React组件
│   │   ├── App.jsx        # 主应用组件
│   │   └── main.jsx       # 入口文件
│   └── package.json
├── pdf-converter-backend/  # 后端Node.js服务
│   ├── server.js          # 主服务器文件
│   ├── uploads/           # 上传文件目录
│   ├── output/            # 转换结果目录
│   └── temp/              # 临时文件目录
└── README.md
```

### 扩展功能

可以考虑添加的功能：
- 批量处理队列管理
- 用户认证和权限控制
- 转换历史记录
- 自定义转换参数
- 云存储集成

## 许可证

本项目基于MIT许可证开源。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 联系方式

如有问题或建议，请通过GitHub Issues联系。
