const express = require('express');
const path = require('path');

const app = express();
const PORT = 8080;

// 提供静态文件
app.use(express.static(path.join(__dirname, 'pdf-converter/dist')));

// 处理所有路由，返回index.html（用于SPA）
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'pdf-converter/dist/index.html'));
});

app.listen(PORT, '0.0.0.0', () => {
  console.log(`前端服务器运行在 http://localhost:${PORT}`);
  console.log(`网络访问: http://0.0.0.0:${PORT}`);
});
