(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const u of document.querySelectorAll('link[rel="modulepreload"]'))a(u);new MutationObserver(u=>{for(const d of u)if(d.type==="childList")for(const f of d.addedNodes)f.tagName==="LINK"&&f.rel==="modulepreload"&&a(f)}).observe(document,{childList:!0,subtree:!0});function o(u){const d={};return u.integrity&&(d.integrity=u.integrity),u.referrerPolicy&&(d.referrerPolicy=u.referrerPolicy),u.crossOrigin==="use-credentials"?d.credentials="include":u.crossOrigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function a(u){if(u.ep)return;u.ep=!0;const d=o(u);fetch(u.href,d)}})();function Cl(i){return i&&i.__esModule&&Object.prototype.hasOwnProperty.call(i,"default")?i.default:i}var Va={exports:{}},gi={},Wa={exports:{}},le={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var wp;function nh(){if(wp)return le;wp=1;var i=Symbol.for("react.element"),r=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),d=Symbol.for("react.provider"),f=Symbol.for("react.context"),g=Symbol.for("react.forward_ref"),E=Symbol.for("react.suspense"),_=Symbol.for("react.memo"),b=Symbol.for("react.lazy"),N=Symbol.iterator;function z(x){return x===null||typeof x!="object"?null:(x=N&&x[N]||x["@@iterator"],typeof x=="function"?x:null)}var J={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},D=Object.assign,L={};function P(x,j,re){this.props=x,this.context=j,this.refs=L,this.updater=re||J}P.prototype.isReactComponent={},P.prototype.setState=function(x,j){if(typeof x!="object"&&typeof x!="function"&&x!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,x,j,"setState")},P.prototype.forceUpdate=function(x){this.updater.enqueueForceUpdate(this,x,"forceUpdate")};function se(){}se.prototype=P.prototype;function Q(x,j,re){this.props=x,this.context=j,this.refs=L,this.updater=re||J}var B=Q.prototype=new se;B.constructor=Q,D(B,P.prototype),B.isPureReactComponent=!0;var U=Array.isArray,ne=Object.prototype.hasOwnProperty,ce={current:null},ue={key:!0,ref:!0,__self:!0,__source:!0};function ke(x,j,re){var te,pe={},oe=null,ve=null;if(j!=null)for(te in j.ref!==void 0&&(ve=j.ref),j.key!==void 0&&(oe=""+j.key),j)ne.call(j,te)&&!ue.hasOwnProperty(te)&&(pe[te]=j[te]);var me=arguments.length-2;if(me===1)pe.children=re;else if(1<me){for(var xe=Array(me),He=0;He<me;He++)xe[He]=arguments[He+2];pe.children=xe}if(x&&x.defaultProps)for(te in me=x.defaultProps,me)pe[te]===void 0&&(pe[te]=me[te]);return{$$typeof:i,type:x,key:oe,ref:ve,props:pe,_owner:ce.current}}function ze(x,j){return{$$typeof:i,type:x.type,key:j,ref:x.ref,props:x.props,_owner:x._owner}}function Je(x){return typeof x=="object"&&x!==null&&x.$$typeof===i}function ae(x){var j={"=":"=0",":":"=2"};return"$"+x.replace(/[=:]/g,function(re){return j[re]})}var Ie=/\/+/g;function Be(x,j){return typeof x=="object"&&x!==null&&x.key!=null?ae(""+x.key):j.toString(36)}function ye(x,j,re,te,pe){var oe=typeof x;(oe==="undefined"||oe==="boolean")&&(x=null);var ve=!1;if(x===null)ve=!0;else switch(oe){case"string":case"number":ve=!0;break;case"object":switch(x.$$typeof){case i:case r:ve=!0}}if(ve)return ve=x,pe=pe(ve),x=te===""?"."+Be(ve,0):te,U(pe)?(re="",x!=null&&(re=x.replace(Ie,"$&/")+"/"),ye(pe,j,re,"",function(He){return He})):pe!=null&&(Je(pe)&&(pe=ze(pe,re+(!pe.key||ve&&ve.key===pe.key?"":(""+pe.key).replace(Ie,"$&/")+"/")+x)),j.push(pe)),1;if(ve=0,te=te===""?".":te+":",U(x))for(var me=0;me<x.length;me++){oe=x[me];var xe=te+Be(oe,me);ve+=ye(oe,j,re,xe,pe)}else if(xe=z(x),typeof xe=="function")for(x=xe.call(x),me=0;!(oe=x.next()).done;)oe=oe.value,xe=te+Be(oe,me++),ve+=ye(oe,j,re,xe,pe);else if(oe==="object")throw j=String(x),Error("Objects are not valid as a React child (found: "+(j==="[object Object]"?"object with keys {"+Object.keys(x).join(", ")+"}":j)+"). If you meant to render a collection of children, use an array instead.");return ve}function je(x,j,re){if(x==null)return x;var te=[],pe=0;return ye(x,te,"","",function(oe){return j.call(re,oe,pe++)}),te}function Ge(x){if(x._status===-1){var j=x._result;j=j(),j.then(function(re){(x._status===0||x._status===-1)&&(x._status=1,x._result=re)},function(re){(x._status===0||x._status===-1)&&(x._status=2,x._result=re)}),x._status===-1&&(x._status=0,x._result=j)}if(x._status===1)return x._result.default;throw x._result}var _e={current:null},F={transition:null},V={ReactCurrentDispatcher:_e,ReactCurrentBatchConfig:F,ReactCurrentOwner:ce};function q(){throw Error("act(...) is not supported in production builds of React.")}return le.Children={map:je,forEach:function(x,j,re){je(x,function(){j.apply(this,arguments)},re)},count:function(x){var j=0;return je(x,function(){j++}),j},toArray:function(x){return je(x,function(j){return j})||[]},only:function(x){if(!Je(x))throw Error("React.Children.only expected to receive a single React element child.");return x}},le.Component=P,le.Fragment=o,le.Profiler=u,le.PureComponent=Q,le.StrictMode=a,le.Suspense=E,le.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=V,le.act=q,le.cloneElement=function(x,j,re){if(x==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+x+".");var te=D({},x.props),pe=x.key,oe=x.ref,ve=x._owner;if(j!=null){if(j.ref!==void 0&&(oe=j.ref,ve=ce.current),j.key!==void 0&&(pe=""+j.key),x.type&&x.type.defaultProps)var me=x.type.defaultProps;for(xe in j)ne.call(j,xe)&&!ue.hasOwnProperty(xe)&&(te[xe]=j[xe]===void 0&&me!==void 0?me[xe]:j[xe])}var xe=arguments.length-2;if(xe===1)te.children=re;else if(1<xe){me=Array(xe);for(var He=0;He<xe;He++)me[He]=arguments[He+2];te.children=me}return{$$typeof:i,type:x.type,key:pe,ref:oe,props:te,_owner:ve}},le.createContext=function(x){return x={$$typeof:f,_currentValue:x,_currentValue2:x,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},x.Provider={$$typeof:d,_context:x},x.Consumer=x},le.createElement=ke,le.createFactory=function(x){var j=ke.bind(null,x);return j.type=x,j},le.createRef=function(){return{current:null}},le.forwardRef=function(x){return{$$typeof:g,render:x}},le.isValidElement=Je,le.lazy=function(x){return{$$typeof:b,_payload:{_status:-1,_result:x},_init:Ge}},le.memo=function(x,j){return{$$typeof:_,type:x,compare:j===void 0?null:j}},le.startTransition=function(x){var j=F.transition;F.transition={};try{x()}finally{F.transition=j}},le.unstable_act=q,le.useCallback=function(x,j){return _e.current.useCallback(x,j)},le.useContext=function(x){return _e.current.useContext(x)},le.useDebugValue=function(){},le.useDeferredValue=function(x){return _e.current.useDeferredValue(x)},le.useEffect=function(x,j){return _e.current.useEffect(x,j)},le.useId=function(){return _e.current.useId()},le.useImperativeHandle=function(x,j,re){return _e.current.useImperativeHandle(x,j,re)},le.useInsertionEffect=function(x,j){return _e.current.useInsertionEffect(x,j)},le.useLayoutEffect=function(x,j){return _e.current.useLayoutEffect(x,j)},le.useMemo=function(x,j){return _e.current.useMemo(x,j)},le.useReducer=function(x,j,re){return _e.current.useReducer(x,j,re)},le.useRef=function(x){return _e.current.useRef(x)},le.useState=function(x){return _e.current.useState(x)},le.useSyncExternalStore=function(x,j,re){return _e.current.useSyncExternalStore(x,j,re)},le.useTransition=function(){return _e.current.useTransition()},le.version="18.3.1",le}var kp;function Nl(){return kp||(kp=1,Wa.exports=nh()),Wa.exports}/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Sp;function rh(){if(Sp)return gi;Sp=1;var i=Nl(),r=Symbol.for("react.element"),o=Symbol.for("react.fragment"),a=Object.prototype.hasOwnProperty,u=i.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,d={key:!0,ref:!0,__self:!0,__source:!0};function f(g,E,_){var b,N={},z=null,J=null;_!==void 0&&(z=""+_),E.key!==void 0&&(z=""+E.key),E.ref!==void 0&&(J=E.ref);for(b in E)a.call(E,b)&&!d.hasOwnProperty(b)&&(N[b]=E[b]);if(g&&g.defaultProps)for(b in E=g.defaultProps,E)N[b]===void 0&&(N[b]=E[b]);return{$$typeof:r,type:g,key:z,ref:J,props:N,_owner:u.current}}return gi.Fragment=o,gi.jsx=f,gi.jsxs=f,gi}var Ep;function ih(){return Ep||(Ep=1,Va.exports=rh()),Va.exports}var v=ih(),ee=Nl();const oh=Cl(ee);var Do={},Ka={exports:{}},dt={},Qa={exports:{}},Ja={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var _p;function sh(){return _p||(_p=1,function(i){function r(F,V){var q=F.length;F.push(V);e:for(;0<q;){var x=q-1>>>1,j=F[x];if(0<u(j,V))F[x]=V,F[q]=j,q=x;else break e}}function o(F){return F.length===0?null:F[0]}function a(F){if(F.length===0)return null;var V=F[0],q=F.pop();if(q!==V){F[0]=q;e:for(var x=0,j=F.length,re=j>>>1;x<re;){var te=2*(x+1)-1,pe=F[te],oe=te+1,ve=F[oe];if(0>u(pe,q))oe<j&&0>u(ve,pe)?(F[x]=ve,F[oe]=q,x=oe):(F[x]=pe,F[te]=q,x=te);else if(oe<j&&0>u(ve,q))F[x]=ve,F[oe]=q,x=oe;else break e}}return V}function u(F,V){var q=F.sortIndex-V.sortIndex;return q!==0?q:F.id-V.id}if(typeof performance=="object"&&typeof performance.now=="function"){var d=performance;i.unstable_now=function(){return d.now()}}else{var f=Date,g=f.now();i.unstable_now=function(){return f.now()-g}}var E=[],_=[],b=1,N=null,z=3,J=!1,D=!1,L=!1,P=typeof setTimeout=="function"?setTimeout:null,se=typeof clearTimeout=="function"?clearTimeout:null,Q=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function B(F){for(var V=o(_);V!==null;){if(V.callback===null)a(_);else if(V.startTime<=F)a(_),V.sortIndex=V.expirationTime,r(E,V);else break;V=o(_)}}function U(F){if(L=!1,B(F),!D)if(o(E)!==null)D=!0,Ge(ne);else{var V=o(_);V!==null&&_e(U,V.startTime-F)}}function ne(F,V){D=!1,L&&(L=!1,se(ke),ke=-1),J=!0;var q=z;try{for(B(V),N=o(E);N!==null&&(!(N.expirationTime>V)||F&&!ae());){var x=N.callback;if(typeof x=="function"){N.callback=null,z=N.priorityLevel;var j=x(N.expirationTime<=V);V=i.unstable_now(),typeof j=="function"?N.callback=j:N===o(E)&&a(E),B(V)}else a(E);N=o(E)}if(N!==null)var re=!0;else{var te=o(_);te!==null&&_e(U,te.startTime-V),re=!1}return re}finally{N=null,z=q,J=!1}}var ce=!1,ue=null,ke=-1,ze=5,Je=-1;function ae(){return!(i.unstable_now()-Je<ze)}function Ie(){if(ue!==null){var F=i.unstable_now();Je=F;var V=!0;try{V=ue(!0,F)}finally{V?Be():(ce=!1,ue=null)}}else ce=!1}var Be;if(typeof Q=="function")Be=function(){Q(Ie)};else if(typeof MessageChannel<"u"){var ye=new MessageChannel,je=ye.port2;ye.port1.onmessage=Ie,Be=function(){je.postMessage(null)}}else Be=function(){P(Ie,0)};function Ge(F){ue=F,ce||(ce=!0,Be())}function _e(F,V){ke=P(function(){F(i.unstable_now())},V)}i.unstable_IdlePriority=5,i.unstable_ImmediatePriority=1,i.unstable_LowPriority=4,i.unstable_NormalPriority=3,i.unstable_Profiling=null,i.unstable_UserBlockingPriority=2,i.unstable_cancelCallback=function(F){F.callback=null},i.unstable_continueExecution=function(){D||J||(D=!0,Ge(ne))},i.unstable_forceFrameRate=function(F){0>F||125<F?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):ze=0<F?Math.floor(1e3/F):5},i.unstable_getCurrentPriorityLevel=function(){return z},i.unstable_getFirstCallbackNode=function(){return o(E)},i.unstable_next=function(F){switch(z){case 1:case 2:case 3:var V=3;break;default:V=z}var q=z;z=V;try{return F()}finally{z=q}},i.unstable_pauseExecution=function(){},i.unstable_requestPaint=function(){},i.unstable_runWithPriority=function(F,V){switch(F){case 1:case 2:case 3:case 4:case 5:break;default:F=3}var q=z;z=F;try{return V()}finally{z=q}},i.unstable_scheduleCallback=function(F,V,q){var x=i.unstable_now();switch(typeof q=="object"&&q!==null?(q=q.delay,q=typeof q=="number"&&0<q?x+q:x):q=x,F){case 1:var j=-1;break;case 2:j=250;break;case 5:j=**********;break;case 4:j=1e4;break;default:j=5e3}return j=q+j,F={id:b++,callback:V,priorityLevel:F,startTime:q,expirationTime:j,sortIndex:-1},q>x?(F.sortIndex=q,r(_,F),o(E)===null&&F===o(_)&&(L?(se(ke),ke=-1):L=!0,_e(U,q-x))):(F.sortIndex=j,r(E,F),D||J||(D=!0,Ge(ne))),F},i.unstable_shouldYield=ae,i.unstable_wrapCallback=function(F){var V=z;return function(){var q=z;z=V;try{return F.apply(this,arguments)}finally{z=q}}}}(Ja)),Ja}var bp;function ah(){return bp||(bp=1,Qa.exports=sh()),Qa.exports}/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Cp;function lh(){if(Cp)return dt;Cp=1;var i=Nl(),r=ah();function o(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var a=new Set,u={};function d(e,t){f(e,t),f(e+"Capture",t)}function f(e,t){for(u[e]=t,e=0;e<t.length;e++)a.add(t[e])}var g=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),E=Object.prototype.hasOwnProperty,_=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,b={},N={};function z(e){return E.call(N,e)?!0:E.call(b,e)?!1:_.test(e)?N[e]=!0:(b[e]=!0,!1)}function J(e,t,n,s){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return s?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function D(e,t,n,s){if(t===null||typeof t>"u"||J(e,t,n,s))return!0;if(s)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function L(e,t,n,s,l,c,p){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=s,this.attributeNamespace=l,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=c,this.removeEmptyString=p}var P={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){P[e]=new L(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];P[t]=new L(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){P[e]=new L(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){P[e]=new L(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){P[e]=new L(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){P[e]=new L(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){P[e]=new L(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){P[e]=new L(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){P[e]=new L(e,5,!1,e.toLowerCase(),null,!1,!1)});var se=/[\-:]([a-z])/g;function Q(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(se,Q);P[t]=new L(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(se,Q);P[t]=new L(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(se,Q);P[t]=new L(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){P[e]=new L(e,1,!1,e.toLowerCase(),null,!1,!1)}),P.xlinkHref=new L("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){P[e]=new L(e,1,!1,e.toLowerCase(),null,!0,!0)});function B(e,t,n,s){var l=P.hasOwnProperty(t)?P[t]:null;(l!==null?l.type!==0:s||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(D(t,n,l,s)&&(n=null),s||l===null?z(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):l.mustUseProperty?e[l.propertyName]=n===null?l.type===3?!1:"":n:(t=l.attributeName,s=l.attributeNamespace,n===null?e.removeAttribute(t):(l=l.type,n=l===3||l===4&&n===!0?"":""+n,s?e.setAttributeNS(s,t,n):e.setAttribute(t,n))))}var U=i.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,ne=Symbol.for("react.element"),ce=Symbol.for("react.portal"),ue=Symbol.for("react.fragment"),ke=Symbol.for("react.strict_mode"),ze=Symbol.for("react.profiler"),Je=Symbol.for("react.provider"),ae=Symbol.for("react.context"),Ie=Symbol.for("react.forward_ref"),Be=Symbol.for("react.suspense"),ye=Symbol.for("react.suspense_list"),je=Symbol.for("react.memo"),Ge=Symbol.for("react.lazy"),_e=Symbol.for("react.offscreen"),F=Symbol.iterator;function V(e){return e===null||typeof e!="object"?null:(e=F&&e[F]||e["@@iterator"],typeof e=="function"?e:null)}var q=Object.assign,x;function j(e){if(x===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);x=t&&t[1]||""}return`
`+x+e}var re=!1;function te(e,t){if(!e||re)return"";re=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(S){var s=S}Reflect.construct(e,[],t)}else{try{t.call()}catch(S){s=S}e.call(t.prototype)}else{try{throw Error()}catch(S){s=S}e()}}catch(S){if(S&&s&&typeof S.stack=="string"){for(var l=S.stack.split(`
`),c=s.stack.split(`
`),p=l.length-1,m=c.length-1;1<=p&&0<=m&&l[p]!==c[m];)m--;for(;1<=p&&0<=m;p--,m--)if(l[p]!==c[m]){if(p!==1||m!==1)do if(p--,m--,0>m||l[p]!==c[m]){var h=`
`+l[p].replace(" at new "," at ");return e.displayName&&h.includes("<anonymous>")&&(h=h.replace("<anonymous>",e.displayName)),h}while(1<=p&&0<=m);break}}}finally{re=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?j(e):""}function pe(e){switch(e.tag){case 5:return j(e.type);case 16:return j("Lazy");case 13:return j("Suspense");case 19:return j("SuspenseList");case 0:case 2:case 15:return e=te(e.type,!1),e;case 11:return e=te(e.type.render,!1),e;case 1:return e=te(e.type,!0),e;default:return""}}function oe(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case ue:return"Fragment";case ce:return"Portal";case ze:return"Profiler";case ke:return"StrictMode";case Be:return"Suspense";case ye:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case ae:return(e.displayName||"Context")+".Consumer";case Je:return(e._context.displayName||"Context")+".Provider";case Ie:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case je:return t=e.displayName||null,t!==null?t:oe(e.type)||"Memo";case Ge:t=e._payload,e=e._init;try{return oe(e(t))}catch{}}return null}function ve(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return oe(t);case 8:return t===ke?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function me(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function xe(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function He(e){var t=xe(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),s=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var l=n.get,c=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return l.call(this)},set:function(p){s=""+p,c.call(this,p)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return s},setValue:function(p){s=""+p},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Wt(e){e._valueTracker||(e._valueTracker=He(e))}function Kt(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),s="";return e&&(s=xe(e)?e.checked?"true":"false":e.value),e=s,e!==n?(t.setValue(e),!0):!1}function Nn(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Xn(e,t){var n=t.checked;return q({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Rr(e,t){var n=t.defaultValue==null?"":t.defaultValue,s=t.checked!=null?t.checked:t.defaultChecked;n=me(t.value!=null?t.value:n),e._wrapperState={initialChecked:s,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Tr(e,t){t=t.checked,t!=null&&B(e,"checked",t,!1)}function Lt(e,t){Tr(e,t);var n=me(t.value),s=t.type;if(n!=null)s==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(s==="submit"||s==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?rn(e,t.type,n):t.hasOwnProperty("defaultValue")&&rn(e,t.type,me(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Gn(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var s=t.type;if(!(s!=="submit"&&s!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function rn(e,t,n){(t!=="number"||Nn(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Ft=Array.isArray;function on(e,t,n,s){if(e=e.options,t){t={};for(var l=0;l<n.length;l++)t["$"+n[l]]=!0;for(n=0;n<e.length;n++)l=t.hasOwnProperty("$"+e[n].value),e[n].selected!==l&&(e[n].selected=l),l&&s&&(e[n].defaultSelected=!0)}else{for(n=""+me(n),t=null,l=0;l<e.length;l++){if(e[l].value===n){e[l].selected=!0,s&&(e[l].defaultSelected=!0);return}t!==null||e[l].disabled||(t=e[l])}t!==null&&(t.selected=!0)}}function Or(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(o(91));return q({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function bi(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(o(92));if(Ft(n)){if(1<n.length)throw Error(o(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:me(n)}}function I(e,t){var n=me(t.value),s=me(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),s!=null&&(e.defaultValue=""+s)}function G(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Re(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Ae(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Re(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Ve,sn=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,s,l){MSApp.execUnsafeLocalFunction(function(){return e(t,n,s,l)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Ve=Ve||document.createElement("div"),Ve.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Ve.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function xt(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Qt={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Pr=["Webkit","ms","Moz","O"];Object.keys(Qt).forEach(function(e){Pr.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Qt[t]=Qt[e]})});function Zn(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Qt.hasOwnProperty(e)&&Qt[e]?(""+t).trim():t+"px"}function jn(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var s=n.indexOf("--")===0,l=Zn(n,t[n],s);n==="float"&&(n="cssFloat"),s?e.setProperty(n,l):e[n]=l}}var Ar=q({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Rn(e,t){if(t){if(Ar[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(o(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(o(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(o(61))}if(t.style!=null&&typeof t.style!="object")throw Error(o(62))}}function Tn(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var On=null;function Dr(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var as=null,er=null,tr=null;function Fl(e){if(e=ni(e)){if(typeof as!="function")throw Error(o(280));var t=e.stateNode;t&&(t=Qi(t),as(e.stateNode,e.type,t))}}function Il(e){er?tr?tr.push(e):tr=[e]:er=e}function Bl(){if(er){var e=er,t=tr;if(tr=er=null,Fl(e),t)for(e=0;e<t.length;e++)Fl(t[e])}}function Ml(e,t){return e(t)}function Ul(){}var ls=!1;function ql(e,t,n){if(ls)return e(t,n);ls=!0;try{return Ml(e,t,n)}finally{ls=!1,(er!==null||tr!==null)&&(Ul(),Bl())}}function zr(e,t){var n=e.stateNode;if(n===null)return null;var s=Qi(n);if(s===null)return null;n=s[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(s=!s.disabled)||(e=e.type,s=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!s;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(o(231,t,typeof n));return n}var cs=!1;if(g)try{var Lr={};Object.defineProperty(Lr,"passive",{get:function(){cs=!0}}),window.addEventListener("test",Lr,Lr),window.removeEventListener("test",Lr,Lr)}catch{cs=!1}function lf(e,t,n,s,l,c,p,m,h){var S=Array.prototype.slice.call(arguments,3);try{t.apply(n,S)}catch(T){this.onError(T)}}var Fr=!1,Ci=null,Ni=!1,us=null,cf={onError:function(e){Fr=!0,Ci=e}};function uf(e,t,n,s,l,c,p,m,h){Fr=!1,Ci=null,lf.apply(cf,arguments)}function pf(e,t,n,s,l,c,p,m,h){if(uf.apply(this,arguments),Fr){if(Fr){var S=Ci;Fr=!1,Ci=null}else throw Error(o(198));Ni||(Ni=!0,us=S)}}function Pn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function $l(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Hl(e){if(Pn(e)!==e)throw Error(o(188))}function df(e){var t=e.alternate;if(!t){if(t=Pn(e),t===null)throw Error(o(188));return t!==e?null:e}for(var n=e,s=t;;){var l=n.return;if(l===null)break;var c=l.alternate;if(c===null){if(s=l.return,s!==null){n=s;continue}break}if(l.child===c.child){for(c=l.child;c;){if(c===n)return Hl(l),e;if(c===s)return Hl(l),t;c=c.sibling}throw Error(o(188))}if(n.return!==s.return)n=l,s=c;else{for(var p=!1,m=l.child;m;){if(m===n){p=!0,n=l,s=c;break}if(m===s){p=!0,s=l,n=c;break}m=m.sibling}if(!p){for(m=c.child;m;){if(m===n){p=!0,n=c,s=l;break}if(m===s){p=!0,s=c,n=l;break}m=m.sibling}if(!p)throw Error(o(189))}}if(n.alternate!==s)throw Error(o(190))}if(n.tag!==3)throw Error(o(188));return n.stateNode.current===n?e:t}function Vl(e){return e=df(e),e!==null?Wl(e):null}function Wl(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Wl(e);if(t!==null)return t;e=e.sibling}return null}var Kl=r.unstable_scheduleCallback,Ql=r.unstable_cancelCallback,ff=r.unstable_shouldYield,mf=r.unstable_requestPaint,Le=r.unstable_now,hf=r.unstable_getCurrentPriorityLevel,ps=r.unstable_ImmediatePriority,Jl=r.unstable_UserBlockingPriority,ji=r.unstable_NormalPriority,vf=r.unstable_LowPriority,Yl=r.unstable_IdlePriority,Ri=null,It=null;function gf(e){if(It&&typeof It.onCommitFiberRoot=="function")try{It.onCommitFiberRoot(Ri,e,void 0,(e.current.flags&128)===128)}catch{}}var Nt=Math.clz32?Math.clz32:wf,yf=Math.log,xf=Math.LN2;function wf(e){return e>>>=0,e===0?32:31-(yf(e)/xf|0)|0}var Ti=64,Oi=4194304;function Ir(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Pi(e,t){var n=e.pendingLanes;if(n===0)return 0;var s=0,l=e.suspendedLanes,c=e.pingedLanes,p=n&268435455;if(p!==0){var m=p&~l;m!==0?s=Ir(m):(c&=p,c!==0&&(s=Ir(c)))}else p=n&~l,p!==0?s=Ir(p):c!==0&&(s=Ir(c));if(s===0)return 0;if(t!==0&&t!==s&&(t&l)===0&&(l=s&-s,c=t&-t,l>=c||l===16&&(c&4194240)!==0))return t;if((s&4)!==0&&(s|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=s;0<t;)n=31-Nt(t),l=1<<n,s|=e[n],t&=~l;return s}function kf(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Sf(e,t){for(var n=e.suspendedLanes,s=e.pingedLanes,l=e.expirationTimes,c=e.pendingLanes;0<c;){var p=31-Nt(c),m=1<<p,h=l[p];h===-1?((m&n)===0||(m&s)!==0)&&(l[p]=kf(m,t)):h<=t&&(e.expiredLanes|=m),c&=~m}}function ds(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Xl(){var e=Ti;return Ti<<=1,(Ti&4194240)===0&&(Ti=64),e}function fs(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Br(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Nt(t),e[t]=n}function Ef(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var s=e.eventTimes;for(e=e.expirationTimes;0<n;){var l=31-Nt(n),c=1<<l;t[l]=0,s[l]=-1,e[l]=-1,n&=~c}}function ms(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var s=31-Nt(n),l=1<<s;l&t|e[s]&t&&(e[s]|=t),n&=~l}}var ge=0;function Gl(e){return e&=-e,1<e?4<e?(e&268435455)!==0?16:536870912:4:1}var Zl,hs,ec,tc,nc,vs=!1,Ai=[],an=null,ln=null,cn=null,Mr=new Map,Ur=new Map,un=[],_f="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function rc(e,t){switch(e){case"focusin":case"focusout":an=null;break;case"dragenter":case"dragleave":ln=null;break;case"mouseover":case"mouseout":cn=null;break;case"pointerover":case"pointerout":Mr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Ur.delete(t.pointerId)}}function qr(e,t,n,s,l,c){return e===null||e.nativeEvent!==c?(e={blockedOn:t,domEventName:n,eventSystemFlags:s,nativeEvent:c,targetContainers:[l]},t!==null&&(t=ni(t),t!==null&&hs(t)),e):(e.eventSystemFlags|=s,t=e.targetContainers,l!==null&&t.indexOf(l)===-1&&t.push(l),e)}function bf(e,t,n,s,l){switch(t){case"focusin":return an=qr(an,e,t,n,s,l),!0;case"dragenter":return ln=qr(ln,e,t,n,s,l),!0;case"mouseover":return cn=qr(cn,e,t,n,s,l),!0;case"pointerover":var c=l.pointerId;return Mr.set(c,qr(Mr.get(c)||null,e,t,n,s,l)),!0;case"gotpointercapture":return c=l.pointerId,Ur.set(c,qr(Ur.get(c)||null,e,t,n,s,l)),!0}return!1}function ic(e){var t=An(e.target);if(t!==null){var n=Pn(t);if(n!==null){if(t=n.tag,t===13){if(t=$l(n),t!==null){e.blockedOn=t,nc(e.priority,function(){ec(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Di(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=ys(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var s=new n.constructor(n.type,n);On=s,n.target.dispatchEvent(s),On=null}else return t=ni(n),t!==null&&hs(t),e.blockedOn=n,!1;t.shift()}return!0}function oc(e,t,n){Di(e)&&n.delete(t)}function Cf(){vs=!1,an!==null&&Di(an)&&(an=null),ln!==null&&Di(ln)&&(ln=null),cn!==null&&Di(cn)&&(cn=null),Mr.forEach(oc),Ur.forEach(oc)}function $r(e,t){e.blockedOn===t&&(e.blockedOn=null,vs||(vs=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,Cf)))}function Hr(e){function t(l){return $r(l,e)}if(0<Ai.length){$r(Ai[0],e);for(var n=1;n<Ai.length;n++){var s=Ai[n];s.blockedOn===e&&(s.blockedOn=null)}}for(an!==null&&$r(an,e),ln!==null&&$r(ln,e),cn!==null&&$r(cn,e),Mr.forEach(t),Ur.forEach(t),n=0;n<un.length;n++)s=un[n],s.blockedOn===e&&(s.blockedOn=null);for(;0<un.length&&(n=un[0],n.blockedOn===null);)ic(n),n.blockedOn===null&&un.shift()}var nr=U.ReactCurrentBatchConfig,zi=!0;function Nf(e,t,n,s){var l=ge,c=nr.transition;nr.transition=null;try{ge=1,gs(e,t,n,s)}finally{ge=l,nr.transition=c}}function jf(e,t,n,s){var l=ge,c=nr.transition;nr.transition=null;try{ge=4,gs(e,t,n,s)}finally{ge=l,nr.transition=c}}function gs(e,t,n,s){if(zi){var l=ys(e,t,n,s);if(l===null)zs(e,t,s,Li,n),rc(e,s);else if(bf(l,e,t,n,s))s.stopPropagation();else if(rc(e,s),t&4&&-1<_f.indexOf(e)){for(;l!==null;){var c=ni(l);if(c!==null&&Zl(c),c=ys(e,t,n,s),c===null&&zs(e,t,s,Li,n),c===l)break;l=c}l!==null&&s.stopPropagation()}else zs(e,t,s,null,n)}}var Li=null;function ys(e,t,n,s){if(Li=null,e=Dr(s),e=An(e),e!==null)if(t=Pn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=$l(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Li=e,null}function sc(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(hf()){case ps:return 1;case Jl:return 4;case ji:case vf:return 16;case Yl:return 536870912;default:return 16}default:return 16}}var pn=null,xs=null,Fi=null;function ac(){if(Fi)return Fi;var e,t=xs,n=t.length,s,l="value"in pn?pn.value:pn.textContent,c=l.length;for(e=0;e<n&&t[e]===l[e];e++);var p=n-e;for(s=1;s<=p&&t[n-s]===l[c-s];s++);return Fi=l.slice(e,1<s?1-s:void 0)}function Ii(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Bi(){return!0}function lc(){return!1}function ht(e){function t(n,s,l,c,p){this._reactName=n,this._targetInst=l,this.type=s,this.nativeEvent=c,this.target=p,this.currentTarget=null;for(var m in e)e.hasOwnProperty(m)&&(n=e[m],this[m]=n?n(c):c[m]);return this.isDefaultPrevented=(c.defaultPrevented!=null?c.defaultPrevented:c.returnValue===!1)?Bi:lc,this.isPropagationStopped=lc,this}return q(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Bi)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Bi)},persist:function(){},isPersistent:Bi}),t}var rr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ws=ht(rr),Vr=q({},rr,{view:0,detail:0}),Rf=ht(Vr),ks,Ss,Wr,Mi=q({},Vr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:_s,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Wr&&(Wr&&e.type==="mousemove"?(ks=e.screenX-Wr.screenX,Ss=e.screenY-Wr.screenY):Ss=ks=0,Wr=e),ks)},movementY:function(e){return"movementY"in e?e.movementY:Ss}}),cc=ht(Mi),Tf=q({},Mi,{dataTransfer:0}),Of=ht(Tf),Pf=q({},Vr,{relatedTarget:0}),Es=ht(Pf),Af=q({},rr,{animationName:0,elapsedTime:0,pseudoElement:0}),Df=ht(Af),zf=q({},rr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Lf=ht(zf),Ff=q({},rr,{data:0}),uc=ht(Ff),If={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Bf={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Mf={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Uf(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Mf[e])?!!t[e]:!1}function _s(){return Uf}var qf=q({},Vr,{key:function(e){if(e.key){var t=If[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Ii(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Bf[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:_s,charCode:function(e){return e.type==="keypress"?Ii(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Ii(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),$f=ht(qf),Hf=q({},Mi,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),pc=ht(Hf),Vf=q({},Vr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:_s}),Wf=ht(Vf),Kf=q({},rr,{propertyName:0,elapsedTime:0,pseudoElement:0}),Qf=ht(Kf),Jf=q({},Mi,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Yf=ht(Jf),Xf=[9,13,27,32],bs=g&&"CompositionEvent"in window,Kr=null;g&&"documentMode"in document&&(Kr=document.documentMode);var Gf=g&&"TextEvent"in window&&!Kr,dc=g&&(!bs||Kr&&8<Kr&&11>=Kr),fc=" ",mc=!1;function hc(e,t){switch(e){case"keyup":return Xf.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function vc(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var ir=!1;function Zf(e,t){switch(e){case"compositionend":return vc(t);case"keypress":return t.which!==32?null:(mc=!0,fc);case"textInput":return e=t.data,e===fc&&mc?null:e;default:return null}}function em(e,t){if(ir)return e==="compositionend"||!bs&&hc(e,t)?(e=ac(),Fi=xs=pn=null,ir=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return dc&&t.locale!=="ko"?null:t.data;default:return null}}var tm={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function gc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!tm[e.type]:t==="textarea"}function yc(e,t,n,s){Il(s),t=Vi(t,"onChange"),0<t.length&&(n=new ws("onChange","change",null,n,s),e.push({event:n,listeners:t}))}var Qr=null,Jr=null;function nm(e){Lc(e,0)}function Ui(e){var t=cr(e);if(Kt(t))return e}function rm(e,t){if(e==="change")return t}var xc=!1;if(g){var Cs;if(g){var Ns="oninput"in document;if(!Ns){var wc=document.createElement("div");wc.setAttribute("oninput","return;"),Ns=typeof wc.oninput=="function"}Cs=Ns}else Cs=!1;xc=Cs&&(!document.documentMode||9<document.documentMode)}function kc(){Qr&&(Qr.detachEvent("onpropertychange",Sc),Jr=Qr=null)}function Sc(e){if(e.propertyName==="value"&&Ui(Jr)){var t=[];yc(t,Jr,e,Dr(e)),ql(nm,t)}}function im(e,t,n){e==="focusin"?(kc(),Qr=t,Jr=n,Qr.attachEvent("onpropertychange",Sc)):e==="focusout"&&kc()}function om(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ui(Jr)}function sm(e,t){if(e==="click")return Ui(t)}function am(e,t){if(e==="input"||e==="change")return Ui(t)}function lm(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var jt=typeof Object.is=="function"?Object.is:lm;function Yr(e,t){if(jt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),s=Object.keys(t);if(n.length!==s.length)return!1;for(s=0;s<n.length;s++){var l=n[s];if(!E.call(t,l)||!jt(e[l],t[l]))return!1}return!0}function Ec(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function _c(e,t){var n=Ec(e);e=0;for(var s;n;){if(n.nodeType===3){if(s=e+n.textContent.length,e<=t&&s>=t)return{node:n,offset:t-e};e=s}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Ec(n)}}function bc(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?bc(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Cc(){for(var e=window,t=Nn();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Nn(e.document)}return t}function js(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function cm(e){var t=Cc(),n=e.focusedElem,s=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&bc(n.ownerDocument.documentElement,n)){if(s!==null&&js(n)){if(t=s.start,e=s.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var l=n.textContent.length,c=Math.min(s.start,l);s=s.end===void 0?c:Math.min(s.end,l),!e.extend&&c>s&&(l=s,s=c,c=l),l=_c(n,c);var p=_c(n,s);l&&p&&(e.rangeCount!==1||e.anchorNode!==l.node||e.anchorOffset!==l.offset||e.focusNode!==p.node||e.focusOffset!==p.offset)&&(t=t.createRange(),t.setStart(l.node,l.offset),e.removeAllRanges(),c>s?(e.addRange(t),e.extend(p.node,p.offset)):(t.setEnd(p.node,p.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var um=g&&"documentMode"in document&&11>=document.documentMode,or=null,Rs=null,Xr=null,Ts=!1;function Nc(e,t,n){var s=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Ts||or==null||or!==Nn(s)||(s=or,"selectionStart"in s&&js(s)?s={start:s.selectionStart,end:s.selectionEnd}:(s=(s.ownerDocument&&s.ownerDocument.defaultView||window).getSelection(),s={anchorNode:s.anchorNode,anchorOffset:s.anchorOffset,focusNode:s.focusNode,focusOffset:s.focusOffset}),Xr&&Yr(Xr,s)||(Xr=s,s=Vi(Rs,"onSelect"),0<s.length&&(t=new ws("onSelect","select",null,t,n),e.push({event:t,listeners:s}),t.target=or)))}function qi(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var sr={animationend:qi("Animation","AnimationEnd"),animationiteration:qi("Animation","AnimationIteration"),animationstart:qi("Animation","AnimationStart"),transitionend:qi("Transition","TransitionEnd")},Os={},jc={};g&&(jc=document.createElement("div").style,"AnimationEvent"in window||(delete sr.animationend.animation,delete sr.animationiteration.animation,delete sr.animationstart.animation),"TransitionEvent"in window||delete sr.transitionend.transition);function $i(e){if(Os[e])return Os[e];if(!sr[e])return e;var t=sr[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in jc)return Os[e]=t[n];return e}var Rc=$i("animationend"),Tc=$i("animationiteration"),Oc=$i("animationstart"),Pc=$i("transitionend"),Ac=new Map,Dc="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function dn(e,t){Ac.set(e,t),d(t,[e])}for(var Ps=0;Ps<Dc.length;Ps++){var As=Dc[Ps],pm=As.toLowerCase(),dm=As[0].toUpperCase()+As.slice(1);dn(pm,"on"+dm)}dn(Rc,"onAnimationEnd"),dn(Tc,"onAnimationIteration"),dn(Oc,"onAnimationStart"),dn("dblclick","onDoubleClick"),dn("focusin","onFocus"),dn("focusout","onBlur"),dn(Pc,"onTransitionEnd"),f("onMouseEnter",["mouseout","mouseover"]),f("onMouseLeave",["mouseout","mouseover"]),f("onPointerEnter",["pointerout","pointerover"]),f("onPointerLeave",["pointerout","pointerover"]),d("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),d("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),d("onBeforeInput",["compositionend","keypress","textInput","paste"]),d("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),d("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),d("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Gr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),fm=new Set("cancel close invalid load scroll toggle".split(" ").concat(Gr));function zc(e,t,n){var s=e.type||"unknown-event";e.currentTarget=n,pf(s,t,void 0,e),e.currentTarget=null}function Lc(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var s=e[n],l=s.event;s=s.listeners;e:{var c=void 0;if(t)for(var p=s.length-1;0<=p;p--){var m=s[p],h=m.instance,S=m.currentTarget;if(m=m.listener,h!==c&&l.isPropagationStopped())break e;zc(l,m,S),c=h}else for(p=0;p<s.length;p++){if(m=s[p],h=m.instance,S=m.currentTarget,m=m.listener,h!==c&&l.isPropagationStopped())break e;zc(l,m,S),c=h}}}if(Ni)throw e=us,Ni=!1,us=null,e}function be(e,t){var n=t[Us];n===void 0&&(n=t[Us]=new Set);var s=e+"__bubble";n.has(s)||(Fc(t,e,2,!1),n.add(s))}function Ds(e,t,n){var s=0;t&&(s|=4),Fc(n,e,s,t)}var Hi="_reactListening"+Math.random().toString(36).slice(2);function Zr(e){if(!e[Hi]){e[Hi]=!0,a.forEach(function(n){n!=="selectionchange"&&(fm.has(n)||Ds(n,!1,e),Ds(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Hi]||(t[Hi]=!0,Ds("selectionchange",!1,t))}}function Fc(e,t,n,s){switch(sc(t)){case 1:var l=Nf;break;case 4:l=jf;break;default:l=gs}n=l.bind(null,t,n,e),l=void 0,!cs||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(l=!0),s?l!==void 0?e.addEventListener(t,n,{capture:!0,passive:l}):e.addEventListener(t,n,!0):l!==void 0?e.addEventListener(t,n,{passive:l}):e.addEventListener(t,n,!1)}function zs(e,t,n,s,l){var c=s;if((t&1)===0&&(t&2)===0&&s!==null)e:for(;;){if(s===null)return;var p=s.tag;if(p===3||p===4){var m=s.stateNode.containerInfo;if(m===l||m.nodeType===8&&m.parentNode===l)break;if(p===4)for(p=s.return;p!==null;){var h=p.tag;if((h===3||h===4)&&(h=p.stateNode.containerInfo,h===l||h.nodeType===8&&h.parentNode===l))return;p=p.return}for(;m!==null;){if(p=An(m),p===null)return;if(h=p.tag,h===5||h===6){s=c=p;continue e}m=m.parentNode}}s=s.return}ql(function(){var S=c,T=Dr(n),O=[];e:{var R=Ac.get(e);if(R!==void 0){var M=ws,H=e;switch(e){case"keypress":if(Ii(n)===0)break e;case"keydown":case"keyup":M=$f;break;case"focusin":H="focus",M=Es;break;case"focusout":H="blur",M=Es;break;case"beforeblur":case"afterblur":M=Es;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":M=cc;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":M=Of;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":M=Wf;break;case Rc:case Tc:case Oc:M=Df;break;case Pc:M=Qf;break;case"scroll":M=Rf;break;case"wheel":M=Yf;break;case"copy":case"cut":case"paste":M=Lf;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":M=pc}var W=(t&4)!==0,Fe=!W&&e==="scroll",w=W?R!==null?R+"Capture":null:R;W=[];for(var y=S,k;y!==null;){k=y;var A=k.stateNode;if(k.tag===5&&A!==null&&(k=A,w!==null&&(A=zr(y,w),A!=null&&W.push(ei(y,A,k)))),Fe)break;y=y.return}0<W.length&&(R=new M(R,H,null,n,T),O.push({event:R,listeners:W}))}}if((t&7)===0){e:{if(R=e==="mouseover"||e==="pointerover",M=e==="mouseout"||e==="pointerout",R&&n!==On&&(H=n.relatedTarget||n.fromElement)&&(An(H)||H[Jt]))break e;if((M||R)&&(R=T.window===T?T:(R=T.ownerDocument)?R.defaultView||R.parentWindow:window,M?(H=n.relatedTarget||n.toElement,M=S,H=H?An(H):null,H!==null&&(Fe=Pn(H),H!==Fe||H.tag!==5&&H.tag!==6)&&(H=null)):(M=null,H=S),M!==H)){if(W=cc,A="onMouseLeave",w="onMouseEnter",y="mouse",(e==="pointerout"||e==="pointerover")&&(W=pc,A="onPointerLeave",w="onPointerEnter",y="pointer"),Fe=M==null?R:cr(M),k=H==null?R:cr(H),R=new W(A,y+"leave",M,n,T),R.target=Fe,R.relatedTarget=k,A=null,An(T)===S&&(W=new W(w,y+"enter",H,n,T),W.target=k,W.relatedTarget=Fe,A=W),Fe=A,M&&H)t:{for(W=M,w=H,y=0,k=W;k;k=ar(k))y++;for(k=0,A=w;A;A=ar(A))k++;for(;0<y-k;)W=ar(W),y--;for(;0<k-y;)w=ar(w),k--;for(;y--;){if(W===w||w!==null&&W===w.alternate)break t;W=ar(W),w=ar(w)}W=null}else W=null;M!==null&&Ic(O,R,M,W,!1),H!==null&&Fe!==null&&Ic(O,Fe,H,W,!0)}}e:{if(R=S?cr(S):window,M=R.nodeName&&R.nodeName.toLowerCase(),M==="select"||M==="input"&&R.type==="file")var K=rm;else if(gc(R))if(xc)K=am;else{K=om;var Y=im}else(M=R.nodeName)&&M.toLowerCase()==="input"&&(R.type==="checkbox"||R.type==="radio")&&(K=sm);if(K&&(K=K(e,S))){yc(O,K,n,T);break e}Y&&Y(e,R,S),e==="focusout"&&(Y=R._wrapperState)&&Y.controlled&&R.type==="number"&&rn(R,"number",R.value)}switch(Y=S?cr(S):window,e){case"focusin":(gc(Y)||Y.contentEditable==="true")&&(or=Y,Rs=S,Xr=null);break;case"focusout":Xr=Rs=or=null;break;case"mousedown":Ts=!0;break;case"contextmenu":case"mouseup":case"dragend":Ts=!1,Nc(O,n,T);break;case"selectionchange":if(um)break;case"keydown":case"keyup":Nc(O,n,T)}var X;if(bs)e:{switch(e){case"compositionstart":var Z="onCompositionStart";break e;case"compositionend":Z="onCompositionEnd";break e;case"compositionupdate":Z="onCompositionUpdate";break e}Z=void 0}else ir?hc(e,n)&&(Z="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(Z="onCompositionStart");Z&&(dc&&n.locale!=="ko"&&(ir||Z!=="onCompositionStart"?Z==="onCompositionEnd"&&ir&&(X=ac()):(pn=T,xs="value"in pn?pn.value:pn.textContent,ir=!0)),Y=Vi(S,Z),0<Y.length&&(Z=new uc(Z,e,null,n,T),O.push({event:Z,listeners:Y}),X?Z.data=X:(X=vc(n),X!==null&&(Z.data=X)))),(X=Gf?Zf(e,n):em(e,n))&&(S=Vi(S,"onBeforeInput"),0<S.length&&(T=new uc("onBeforeInput","beforeinput",null,n,T),O.push({event:T,listeners:S}),T.data=X))}Lc(O,t)})}function ei(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Vi(e,t){for(var n=t+"Capture",s=[];e!==null;){var l=e,c=l.stateNode;l.tag===5&&c!==null&&(l=c,c=zr(e,n),c!=null&&s.unshift(ei(e,c,l)),c=zr(e,t),c!=null&&s.push(ei(e,c,l))),e=e.return}return s}function ar(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Ic(e,t,n,s,l){for(var c=t._reactName,p=[];n!==null&&n!==s;){var m=n,h=m.alternate,S=m.stateNode;if(h!==null&&h===s)break;m.tag===5&&S!==null&&(m=S,l?(h=zr(n,c),h!=null&&p.unshift(ei(n,h,m))):l||(h=zr(n,c),h!=null&&p.push(ei(n,h,m)))),n=n.return}p.length!==0&&e.push({event:t,listeners:p})}var mm=/\r\n?/g,hm=/\u0000|\uFFFD/g;function Bc(e){return(typeof e=="string"?e:""+e).replace(mm,`
`).replace(hm,"")}function Wi(e,t,n){if(t=Bc(t),Bc(e)!==t&&n)throw Error(o(425))}function Ki(){}var Ls=null,Fs=null;function Is(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Bs=typeof setTimeout=="function"?setTimeout:void 0,vm=typeof clearTimeout=="function"?clearTimeout:void 0,Mc=typeof Promise=="function"?Promise:void 0,gm=typeof queueMicrotask=="function"?queueMicrotask:typeof Mc<"u"?function(e){return Mc.resolve(null).then(e).catch(ym)}:Bs;function ym(e){setTimeout(function(){throw e})}function Ms(e,t){var n=t,s=0;do{var l=n.nextSibling;if(e.removeChild(n),l&&l.nodeType===8)if(n=l.data,n==="/$"){if(s===0){e.removeChild(l),Hr(t);return}s--}else n!=="$"&&n!=="$?"&&n!=="$!"||s++;n=l}while(n);Hr(t)}function fn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Uc(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var lr=Math.random().toString(36).slice(2),Bt="__reactFiber$"+lr,ti="__reactProps$"+lr,Jt="__reactContainer$"+lr,Us="__reactEvents$"+lr,xm="__reactListeners$"+lr,wm="__reactHandles$"+lr;function An(e){var t=e[Bt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Jt]||n[Bt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Uc(e);e!==null;){if(n=e[Bt])return n;e=Uc(e)}return t}e=n,n=e.parentNode}return null}function ni(e){return e=e[Bt]||e[Jt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function cr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(o(33))}function Qi(e){return e[ti]||null}var qs=[],ur=-1;function mn(e){return{current:e}}function Ce(e){0>ur||(e.current=qs[ur],qs[ur]=null,ur--)}function Se(e,t){ur++,qs[ur]=e.current,e.current=t}var hn={},Ze=mn(hn),at=mn(!1),Dn=hn;function pr(e,t){var n=e.type.contextTypes;if(!n)return hn;var s=e.stateNode;if(s&&s.__reactInternalMemoizedUnmaskedChildContext===t)return s.__reactInternalMemoizedMaskedChildContext;var l={},c;for(c in n)l[c]=t[c];return s&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function lt(e){return e=e.childContextTypes,e!=null}function Ji(){Ce(at),Ce(Ze)}function qc(e,t,n){if(Ze.current!==hn)throw Error(o(168));Se(Ze,t),Se(at,n)}function $c(e,t,n){var s=e.stateNode;if(t=t.childContextTypes,typeof s.getChildContext!="function")return n;s=s.getChildContext();for(var l in s)if(!(l in t))throw Error(o(108,ve(e)||"Unknown",l));return q({},n,s)}function Yi(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||hn,Dn=Ze.current,Se(Ze,e),Se(at,at.current),!0}function Hc(e,t,n){var s=e.stateNode;if(!s)throw Error(o(169));n?(e=$c(e,t,Dn),s.__reactInternalMemoizedMergedChildContext=e,Ce(at),Ce(Ze),Se(Ze,e)):Ce(at),Se(at,n)}var Yt=null,Xi=!1,$s=!1;function Vc(e){Yt===null?Yt=[e]:Yt.push(e)}function km(e){Xi=!0,Vc(e)}function vn(){if(!$s&&Yt!==null){$s=!0;var e=0,t=ge;try{var n=Yt;for(ge=1;e<n.length;e++){var s=n[e];do s=s(!0);while(s!==null)}Yt=null,Xi=!1}catch(l){throw Yt!==null&&(Yt=Yt.slice(e+1)),Kl(ps,vn),l}finally{ge=t,$s=!1}}return null}var dr=[],fr=0,Gi=null,Zi=0,wt=[],kt=0,zn=null,Xt=1,Gt="";function Ln(e,t){dr[fr++]=Zi,dr[fr++]=Gi,Gi=e,Zi=t}function Wc(e,t,n){wt[kt++]=Xt,wt[kt++]=Gt,wt[kt++]=zn,zn=e;var s=Xt;e=Gt;var l=32-Nt(s)-1;s&=~(1<<l),n+=1;var c=32-Nt(t)+l;if(30<c){var p=l-l%5;c=(s&(1<<p)-1).toString(32),s>>=p,l-=p,Xt=1<<32-Nt(t)+l|n<<l|s,Gt=c+e}else Xt=1<<c|n<<l|s,Gt=e}function Hs(e){e.return!==null&&(Ln(e,1),Wc(e,1,0))}function Vs(e){for(;e===Gi;)Gi=dr[--fr],dr[fr]=null,Zi=dr[--fr],dr[fr]=null;for(;e===zn;)zn=wt[--kt],wt[kt]=null,Gt=wt[--kt],wt[kt]=null,Xt=wt[--kt],wt[kt]=null}var vt=null,gt=null,Ne=!1,Rt=null;function Kc(e,t){var n=bt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Qc(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,vt=e,gt=fn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,vt=e,gt=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=zn!==null?{id:Xt,overflow:Gt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=bt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,vt=e,gt=null,!0):!1;default:return!1}}function Ws(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Ks(e){if(Ne){var t=gt;if(t){var n=t;if(!Qc(e,t)){if(Ws(e))throw Error(o(418));t=fn(n.nextSibling);var s=vt;t&&Qc(e,t)?Kc(s,n):(e.flags=e.flags&-4097|2,Ne=!1,vt=e)}}else{if(Ws(e))throw Error(o(418));e.flags=e.flags&-4097|2,Ne=!1,vt=e}}}function Jc(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;vt=e}function eo(e){if(e!==vt)return!1;if(!Ne)return Jc(e),Ne=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Is(e.type,e.memoizedProps)),t&&(t=gt)){if(Ws(e))throw Yc(),Error(o(418));for(;t;)Kc(e,t),t=fn(t.nextSibling)}if(Jc(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(o(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){gt=fn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}gt=null}}else gt=vt?fn(e.stateNode.nextSibling):null;return!0}function Yc(){for(var e=gt;e;)e=fn(e.nextSibling)}function mr(){gt=vt=null,Ne=!1}function Qs(e){Rt===null?Rt=[e]:Rt.push(e)}var Sm=U.ReactCurrentBatchConfig;function ri(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(o(309));var s=n.stateNode}if(!s)throw Error(o(147,e));var l=s,c=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===c?t.ref:(t=function(p){var m=l.refs;p===null?delete m[c]:m[c]=p},t._stringRef=c,t)}if(typeof e!="string")throw Error(o(284));if(!n._owner)throw Error(o(290,e))}return e}function to(e,t){throw e=Object.prototype.toString.call(t),Error(o(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Xc(e){var t=e._init;return t(e._payload)}function Gc(e){function t(w,y){if(e){var k=w.deletions;k===null?(w.deletions=[y],w.flags|=16):k.push(y)}}function n(w,y){if(!e)return null;for(;y!==null;)t(w,y),y=y.sibling;return null}function s(w,y){for(w=new Map;y!==null;)y.key!==null?w.set(y.key,y):w.set(y.index,y),y=y.sibling;return w}function l(w,y){return w=_n(w,y),w.index=0,w.sibling=null,w}function c(w,y,k){return w.index=k,e?(k=w.alternate,k!==null?(k=k.index,k<y?(w.flags|=2,y):k):(w.flags|=2,y)):(w.flags|=1048576,y)}function p(w){return e&&w.alternate===null&&(w.flags|=2),w}function m(w,y,k,A){return y===null||y.tag!==6?(y=Ba(k,w.mode,A),y.return=w,y):(y=l(y,k),y.return=w,y)}function h(w,y,k,A){var K=k.type;return K===ue?T(w,y,k.props.children,A,k.key):y!==null&&(y.elementType===K||typeof K=="object"&&K!==null&&K.$$typeof===Ge&&Xc(K)===y.type)?(A=l(y,k.props),A.ref=ri(w,y,k),A.return=w,A):(A=Co(k.type,k.key,k.props,null,w.mode,A),A.ref=ri(w,y,k),A.return=w,A)}function S(w,y,k,A){return y===null||y.tag!==4||y.stateNode.containerInfo!==k.containerInfo||y.stateNode.implementation!==k.implementation?(y=Ma(k,w.mode,A),y.return=w,y):(y=l(y,k.children||[]),y.return=w,y)}function T(w,y,k,A,K){return y===null||y.tag!==7?(y=Hn(k,w.mode,A,K),y.return=w,y):(y=l(y,k),y.return=w,y)}function O(w,y,k){if(typeof y=="string"&&y!==""||typeof y=="number")return y=Ba(""+y,w.mode,k),y.return=w,y;if(typeof y=="object"&&y!==null){switch(y.$$typeof){case ne:return k=Co(y.type,y.key,y.props,null,w.mode,k),k.ref=ri(w,null,y),k.return=w,k;case ce:return y=Ma(y,w.mode,k),y.return=w,y;case Ge:var A=y._init;return O(w,A(y._payload),k)}if(Ft(y)||V(y))return y=Hn(y,w.mode,k,null),y.return=w,y;to(w,y)}return null}function R(w,y,k,A){var K=y!==null?y.key:null;if(typeof k=="string"&&k!==""||typeof k=="number")return K!==null?null:m(w,y,""+k,A);if(typeof k=="object"&&k!==null){switch(k.$$typeof){case ne:return k.key===K?h(w,y,k,A):null;case ce:return k.key===K?S(w,y,k,A):null;case Ge:return K=k._init,R(w,y,K(k._payload),A)}if(Ft(k)||V(k))return K!==null?null:T(w,y,k,A,null);to(w,k)}return null}function M(w,y,k,A,K){if(typeof A=="string"&&A!==""||typeof A=="number")return w=w.get(k)||null,m(y,w,""+A,K);if(typeof A=="object"&&A!==null){switch(A.$$typeof){case ne:return w=w.get(A.key===null?k:A.key)||null,h(y,w,A,K);case ce:return w=w.get(A.key===null?k:A.key)||null,S(y,w,A,K);case Ge:var Y=A._init;return M(w,y,k,Y(A._payload),K)}if(Ft(A)||V(A))return w=w.get(k)||null,T(y,w,A,K,null);to(y,A)}return null}function H(w,y,k,A){for(var K=null,Y=null,X=y,Z=y=0,Qe=null;X!==null&&Z<k.length;Z++){X.index>Z?(Qe=X,X=null):Qe=X.sibling;var he=R(w,X,k[Z],A);if(he===null){X===null&&(X=Qe);break}e&&X&&he.alternate===null&&t(w,X),y=c(he,y,Z),Y===null?K=he:Y.sibling=he,Y=he,X=Qe}if(Z===k.length)return n(w,X),Ne&&Ln(w,Z),K;if(X===null){for(;Z<k.length;Z++)X=O(w,k[Z],A),X!==null&&(y=c(X,y,Z),Y===null?K=X:Y.sibling=X,Y=X);return Ne&&Ln(w,Z),K}for(X=s(w,X);Z<k.length;Z++)Qe=M(X,w,Z,k[Z],A),Qe!==null&&(e&&Qe.alternate!==null&&X.delete(Qe.key===null?Z:Qe.key),y=c(Qe,y,Z),Y===null?K=Qe:Y.sibling=Qe,Y=Qe);return e&&X.forEach(function(bn){return t(w,bn)}),Ne&&Ln(w,Z),K}function W(w,y,k,A){var K=V(k);if(typeof K!="function")throw Error(o(150));if(k=K.call(k),k==null)throw Error(o(151));for(var Y=K=null,X=y,Z=y=0,Qe=null,he=k.next();X!==null&&!he.done;Z++,he=k.next()){X.index>Z?(Qe=X,X=null):Qe=X.sibling;var bn=R(w,X,he.value,A);if(bn===null){X===null&&(X=Qe);break}e&&X&&bn.alternate===null&&t(w,X),y=c(bn,y,Z),Y===null?K=bn:Y.sibling=bn,Y=bn,X=Qe}if(he.done)return n(w,X),Ne&&Ln(w,Z),K;if(X===null){for(;!he.done;Z++,he=k.next())he=O(w,he.value,A),he!==null&&(y=c(he,y,Z),Y===null?K=he:Y.sibling=he,Y=he);return Ne&&Ln(w,Z),K}for(X=s(w,X);!he.done;Z++,he=k.next())he=M(X,w,Z,he.value,A),he!==null&&(e&&he.alternate!==null&&X.delete(he.key===null?Z:he.key),y=c(he,y,Z),Y===null?K=he:Y.sibling=he,Y=he);return e&&X.forEach(function(th){return t(w,th)}),Ne&&Ln(w,Z),K}function Fe(w,y,k,A){if(typeof k=="object"&&k!==null&&k.type===ue&&k.key===null&&(k=k.props.children),typeof k=="object"&&k!==null){switch(k.$$typeof){case ne:e:{for(var K=k.key,Y=y;Y!==null;){if(Y.key===K){if(K=k.type,K===ue){if(Y.tag===7){n(w,Y.sibling),y=l(Y,k.props.children),y.return=w,w=y;break e}}else if(Y.elementType===K||typeof K=="object"&&K!==null&&K.$$typeof===Ge&&Xc(K)===Y.type){n(w,Y.sibling),y=l(Y,k.props),y.ref=ri(w,Y,k),y.return=w,w=y;break e}n(w,Y);break}else t(w,Y);Y=Y.sibling}k.type===ue?(y=Hn(k.props.children,w.mode,A,k.key),y.return=w,w=y):(A=Co(k.type,k.key,k.props,null,w.mode,A),A.ref=ri(w,y,k),A.return=w,w=A)}return p(w);case ce:e:{for(Y=k.key;y!==null;){if(y.key===Y)if(y.tag===4&&y.stateNode.containerInfo===k.containerInfo&&y.stateNode.implementation===k.implementation){n(w,y.sibling),y=l(y,k.children||[]),y.return=w,w=y;break e}else{n(w,y);break}else t(w,y);y=y.sibling}y=Ma(k,w.mode,A),y.return=w,w=y}return p(w);case Ge:return Y=k._init,Fe(w,y,Y(k._payload),A)}if(Ft(k))return H(w,y,k,A);if(V(k))return W(w,y,k,A);to(w,k)}return typeof k=="string"&&k!==""||typeof k=="number"?(k=""+k,y!==null&&y.tag===6?(n(w,y.sibling),y=l(y,k),y.return=w,w=y):(n(w,y),y=Ba(k,w.mode,A),y.return=w,w=y),p(w)):n(w,y)}return Fe}var hr=Gc(!0),Zc=Gc(!1),no=mn(null),ro=null,vr=null,Js=null;function Ys(){Js=vr=ro=null}function Xs(e){var t=no.current;Ce(no),e._currentValue=t}function Gs(e,t,n){for(;e!==null;){var s=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,s!==null&&(s.childLanes|=t)):s!==null&&(s.childLanes&t)!==t&&(s.childLanes|=t),e===n)break;e=e.return}}function gr(e,t){ro=e,Js=vr=null,e=e.dependencies,e!==null&&e.firstContext!==null&&((e.lanes&t)!==0&&(ct=!0),e.firstContext=null)}function St(e){var t=e._currentValue;if(Js!==e)if(e={context:e,memoizedValue:t,next:null},vr===null){if(ro===null)throw Error(o(308));vr=e,ro.dependencies={lanes:0,firstContext:e}}else vr=vr.next=e;return t}var Fn=null;function Zs(e){Fn===null?Fn=[e]:Fn.push(e)}function eu(e,t,n,s){var l=t.interleaved;return l===null?(n.next=n,Zs(t)):(n.next=l.next,l.next=n),t.interleaved=n,Zt(e,s)}function Zt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var gn=!1;function ea(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function tu(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function en(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function yn(e,t,n){var s=e.updateQueue;if(s===null)return null;if(s=s.shared,(fe&2)!==0){var l=s.pending;return l===null?t.next=t:(t.next=l.next,l.next=t),s.pending=t,Zt(e,n)}return l=s.interleaved,l===null?(t.next=t,Zs(s)):(t.next=l.next,l.next=t),s.interleaved=t,Zt(e,n)}function io(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var s=t.lanes;s&=e.pendingLanes,n|=s,t.lanes=n,ms(e,n)}}function nu(e,t){var n=e.updateQueue,s=e.alternate;if(s!==null&&(s=s.updateQueue,n===s)){var l=null,c=null;if(n=n.firstBaseUpdate,n!==null){do{var p={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};c===null?l=c=p:c=c.next=p,n=n.next}while(n!==null);c===null?l=c=t:c=c.next=t}else l=c=t;n={baseState:s.baseState,firstBaseUpdate:l,lastBaseUpdate:c,shared:s.shared,effects:s.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function oo(e,t,n,s){var l=e.updateQueue;gn=!1;var c=l.firstBaseUpdate,p=l.lastBaseUpdate,m=l.shared.pending;if(m!==null){l.shared.pending=null;var h=m,S=h.next;h.next=null,p===null?c=S:p.next=S,p=h;var T=e.alternate;T!==null&&(T=T.updateQueue,m=T.lastBaseUpdate,m!==p&&(m===null?T.firstBaseUpdate=S:m.next=S,T.lastBaseUpdate=h))}if(c!==null){var O=l.baseState;p=0,T=S=h=null,m=c;do{var R=m.lane,M=m.eventTime;if((s&R)===R){T!==null&&(T=T.next={eventTime:M,lane:0,tag:m.tag,payload:m.payload,callback:m.callback,next:null});e:{var H=e,W=m;switch(R=t,M=n,W.tag){case 1:if(H=W.payload,typeof H=="function"){O=H.call(M,O,R);break e}O=H;break e;case 3:H.flags=H.flags&-65537|128;case 0:if(H=W.payload,R=typeof H=="function"?H.call(M,O,R):H,R==null)break e;O=q({},O,R);break e;case 2:gn=!0}}m.callback!==null&&m.lane!==0&&(e.flags|=64,R=l.effects,R===null?l.effects=[m]:R.push(m))}else M={eventTime:M,lane:R,tag:m.tag,payload:m.payload,callback:m.callback,next:null},T===null?(S=T=M,h=O):T=T.next=M,p|=R;if(m=m.next,m===null){if(m=l.shared.pending,m===null)break;R=m,m=R.next,R.next=null,l.lastBaseUpdate=R,l.shared.pending=null}}while(!0);if(T===null&&(h=O),l.baseState=h,l.firstBaseUpdate=S,l.lastBaseUpdate=T,t=l.shared.interleaved,t!==null){l=t;do p|=l.lane,l=l.next;while(l!==t)}else c===null&&(l.shared.lanes=0);Mn|=p,e.lanes=p,e.memoizedState=O}}function ru(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var s=e[t],l=s.callback;if(l!==null){if(s.callback=null,s=n,typeof l!="function")throw Error(o(191,l));l.call(s)}}}var ii={},Mt=mn(ii),oi=mn(ii),si=mn(ii);function In(e){if(e===ii)throw Error(o(174));return e}function ta(e,t){switch(Se(si,t),Se(oi,e),Se(Mt,ii),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Ae(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Ae(t,e)}Ce(Mt),Se(Mt,t)}function yr(){Ce(Mt),Ce(oi),Ce(si)}function iu(e){In(si.current);var t=In(Mt.current),n=Ae(t,e.type);t!==n&&(Se(oi,e),Se(Mt,n))}function na(e){oi.current===e&&(Ce(Mt),Ce(oi))}var Te=mn(0);function so(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ra=[];function ia(){for(var e=0;e<ra.length;e++)ra[e]._workInProgressVersionPrimary=null;ra.length=0}var ao=U.ReactCurrentDispatcher,oa=U.ReactCurrentBatchConfig,Bn=0,Oe=null,qe=null,We=null,lo=!1,ai=!1,li=0,Em=0;function et(){throw Error(o(321))}function sa(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!jt(e[n],t[n]))return!1;return!0}function aa(e,t,n,s,l,c){if(Bn=c,Oe=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ao.current=e===null||e.memoizedState===null?Nm:jm,e=n(s,l),ai){c=0;do{if(ai=!1,li=0,25<=c)throw Error(o(301));c+=1,We=qe=null,t.updateQueue=null,ao.current=Rm,e=n(s,l)}while(ai)}if(ao.current=po,t=qe!==null&&qe.next!==null,Bn=0,We=qe=Oe=null,lo=!1,t)throw Error(o(300));return e}function la(){var e=li!==0;return li=0,e}function Ut(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return We===null?Oe.memoizedState=We=e:We=We.next=e,We}function Et(){if(qe===null){var e=Oe.alternate;e=e!==null?e.memoizedState:null}else e=qe.next;var t=We===null?Oe.memoizedState:We.next;if(t!==null)We=t,qe=e;else{if(e===null)throw Error(o(310));qe=e,e={memoizedState:qe.memoizedState,baseState:qe.baseState,baseQueue:qe.baseQueue,queue:qe.queue,next:null},We===null?Oe.memoizedState=We=e:We=We.next=e}return We}function ci(e,t){return typeof t=="function"?t(e):t}function ca(e){var t=Et(),n=t.queue;if(n===null)throw Error(o(311));n.lastRenderedReducer=e;var s=qe,l=s.baseQueue,c=n.pending;if(c!==null){if(l!==null){var p=l.next;l.next=c.next,c.next=p}s.baseQueue=l=c,n.pending=null}if(l!==null){c=l.next,s=s.baseState;var m=p=null,h=null,S=c;do{var T=S.lane;if((Bn&T)===T)h!==null&&(h=h.next={lane:0,action:S.action,hasEagerState:S.hasEagerState,eagerState:S.eagerState,next:null}),s=S.hasEagerState?S.eagerState:e(s,S.action);else{var O={lane:T,action:S.action,hasEagerState:S.hasEagerState,eagerState:S.eagerState,next:null};h===null?(m=h=O,p=s):h=h.next=O,Oe.lanes|=T,Mn|=T}S=S.next}while(S!==null&&S!==c);h===null?p=s:h.next=m,jt(s,t.memoizedState)||(ct=!0),t.memoizedState=s,t.baseState=p,t.baseQueue=h,n.lastRenderedState=s}if(e=n.interleaved,e!==null){l=e;do c=l.lane,Oe.lanes|=c,Mn|=c,l=l.next;while(l!==e)}else l===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function ua(e){var t=Et(),n=t.queue;if(n===null)throw Error(o(311));n.lastRenderedReducer=e;var s=n.dispatch,l=n.pending,c=t.memoizedState;if(l!==null){n.pending=null;var p=l=l.next;do c=e(c,p.action),p=p.next;while(p!==l);jt(c,t.memoizedState)||(ct=!0),t.memoizedState=c,t.baseQueue===null&&(t.baseState=c),n.lastRenderedState=c}return[c,s]}function ou(){}function su(e,t){var n=Oe,s=Et(),l=t(),c=!jt(s.memoizedState,l);if(c&&(s.memoizedState=l,ct=!0),s=s.queue,pa(cu.bind(null,n,s,e),[e]),s.getSnapshot!==t||c||We!==null&&We.memoizedState.tag&1){if(n.flags|=2048,ui(9,lu.bind(null,n,s,l,t),void 0,null),Ke===null)throw Error(o(349));(Bn&30)!==0||au(n,t,l)}return l}function au(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Oe.updateQueue,t===null?(t={lastEffect:null,stores:null},Oe.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function lu(e,t,n,s){t.value=n,t.getSnapshot=s,uu(t)&&pu(e)}function cu(e,t,n){return n(function(){uu(t)&&pu(e)})}function uu(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!jt(e,n)}catch{return!0}}function pu(e){var t=Zt(e,1);t!==null&&At(t,e,1,-1)}function du(e){var t=Ut();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:ci,lastRenderedState:e},t.queue=e,e=e.dispatch=Cm.bind(null,Oe,e),[t.memoizedState,e]}function ui(e,t,n,s){return e={tag:e,create:t,destroy:n,deps:s,next:null},t=Oe.updateQueue,t===null?(t={lastEffect:null,stores:null},Oe.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(s=n.next,n.next=e,e.next=s,t.lastEffect=e)),e}function fu(){return Et().memoizedState}function co(e,t,n,s){var l=Ut();Oe.flags|=e,l.memoizedState=ui(1|t,n,void 0,s===void 0?null:s)}function uo(e,t,n,s){var l=Et();s=s===void 0?null:s;var c=void 0;if(qe!==null){var p=qe.memoizedState;if(c=p.destroy,s!==null&&sa(s,p.deps)){l.memoizedState=ui(t,n,c,s);return}}Oe.flags|=e,l.memoizedState=ui(1|t,n,c,s)}function mu(e,t){return co(8390656,8,e,t)}function pa(e,t){return uo(2048,8,e,t)}function hu(e,t){return uo(4,2,e,t)}function vu(e,t){return uo(4,4,e,t)}function gu(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function yu(e,t,n){return n=n!=null?n.concat([e]):null,uo(4,4,gu.bind(null,t,e),n)}function da(){}function xu(e,t){var n=Et();t=t===void 0?null:t;var s=n.memoizedState;return s!==null&&t!==null&&sa(t,s[1])?s[0]:(n.memoizedState=[e,t],e)}function wu(e,t){var n=Et();t=t===void 0?null:t;var s=n.memoizedState;return s!==null&&t!==null&&sa(t,s[1])?s[0]:(e=e(),n.memoizedState=[e,t],e)}function ku(e,t,n){return(Bn&21)===0?(e.baseState&&(e.baseState=!1,ct=!0),e.memoizedState=n):(jt(n,t)||(n=Xl(),Oe.lanes|=n,Mn|=n,e.baseState=!0),t)}function _m(e,t){var n=ge;ge=n!==0&&4>n?n:4,e(!0);var s=oa.transition;oa.transition={};try{e(!1),t()}finally{ge=n,oa.transition=s}}function Su(){return Et().memoizedState}function bm(e,t,n){var s=Sn(e);if(n={lane:s,action:n,hasEagerState:!1,eagerState:null,next:null},Eu(e))_u(t,n);else if(n=eu(e,t,n,s),n!==null){var l=st();At(n,e,s,l),bu(n,t,s)}}function Cm(e,t,n){var s=Sn(e),l={lane:s,action:n,hasEagerState:!1,eagerState:null,next:null};if(Eu(e))_u(t,l);else{var c=e.alternate;if(e.lanes===0&&(c===null||c.lanes===0)&&(c=t.lastRenderedReducer,c!==null))try{var p=t.lastRenderedState,m=c(p,n);if(l.hasEagerState=!0,l.eagerState=m,jt(m,p)){var h=t.interleaved;h===null?(l.next=l,Zs(t)):(l.next=h.next,h.next=l),t.interleaved=l;return}}catch{}finally{}n=eu(e,t,l,s),n!==null&&(l=st(),At(n,e,s,l),bu(n,t,s))}}function Eu(e){var t=e.alternate;return e===Oe||t!==null&&t===Oe}function _u(e,t){ai=lo=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function bu(e,t,n){if((n&4194240)!==0){var s=t.lanes;s&=e.pendingLanes,n|=s,t.lanes=n,ms(e,n)}}var po={readContext:St,useCallback:et,useContext:et,useEffect:et,useImperativeHandle:et,useInsertionEffect:et,useLayoutEffect:et,useMemo:et,useReducer:et,useRef:et,useState:et,useDebugValue:et,useDeferredValue:et,useTransition:et,useMutableSource:et,useSyncExternalStore:et,useId:et,unstable_isNewReconciler:!1},Nm={readContext:St,useCallback:function(e,t){return Ut().memoizedState=[e,t===void 0?null:t],e},useContext:St,useEffect:mu,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,co(4194308,4,gu.bind(null,t,e),n)},useLayoutEffect:function(e,t){return co(4194308,4,e,t)},useInsertionEffect:function(e,t){return co(4,2,e,t)},useMemo:function(e,t){var n=Ut();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var s=Ut();return t=n!==void 0?n(t):t,s.memoizedState=s.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},s.queue=e,e=e.dispatch=bm.bind(null,Oe,e),[s.memoizedState,e]},useRef:function(e){var t=Ut();return e={current:e},t.memoizedState=e},useState:du,useDebugValue:da,useDeferredValue:function(e){return Ut().memoizedState=e},useTransition:function(){var e=du(!1),t=e[0];return e=_m.bind(null,e[1]),Ut().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var s=Oe,l=Ut();if(Ne){if(n===void 0)throw Error(o(407));n=n()}else{if(n=t(),Ke===null)throw Error(o(349));(Bn&30)!==0||au(s,t,n)}l.memoizedState=n;var c={value:n,getSnapshot:t};return l.queue=c,mu(cu.bind(null,s,c,e),[e]),s.flags|=2048,ui(9,lu.bind(null,s,c,n,t),void 0,null),n},useId:function(){var e=Ut(),t=Ke.identifierPrefix;if(Ne){var n=Gt,s=Xt;n=(s&~(1<<32-Nt(s)-1)).toString(32)+n,t=":"+t+"R"+n,n=li++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Em++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},jm={readContext:St,useCallback:xu,useContext:St,useEffect:pa,useImperativeHandle:yu,useInsertionEffect:hu,useLayoutEffect:vu,useMemo:wu,useReducer:ca,useRef:fu,useState:function(){return ca(ci)},useDebugValue:da,useDeferredValue:function(e){var t=Et();return ku(t,qe.memoizedState,e)},useTransition:function(){var e=ca(ci)[0],t=Et().memoizedState;return[e,t]},useMutableSource:ou,useSyncExternalStore:su,useId:Su,unstable_isNewReconciler:!1},Rm={readContext:St,useCallback:xu,useContext:St,useEffect:pa,useImperativeHandle:yu,useInsertionEffect:hu,useLayoutEffect:vu,useMemo:wu,useReducer:ua,useRef:fu,useState:function(){return ua(ci)},useDebugValue:da,useDeferredValue:function(e){var t=Et();return qe===null?t.memoizedState=e:ku(t,qe.memoizedState,e)},useTransition:function(){var e=ua(ci)[0],t=Et().memoizedState;return[e,t]},useMutableSource:ou,useSyncExternalStore:su,useId:Su,unstable_isNewReconciler:!1};function Tt(e,t){if(e&&e.defaultProps){t=q({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function fa(e,t,n,s){t=e.memoizedState,n=n(s,t),n=n==null?t:q({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var fo={isMounted:function(e){return(e=e._reactInternals)?Pn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var s=st(),l=Sn(e),c=en(s,l);c.payload=t,n!=null&&(c.callback=n),t=yn(e,c,l),t!==null&&(At(t,e,l,s),io(t,e,l))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var s=st(),l=Sn(e),c=en(s,l);c.tag=1,c.payload=t,n!=null&&(c.callback=n),t=yn(e,c,l),t!==null&&(At(t,e,l,s),io(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=st(),s=Sn(e),l=en(n,s);l.tag=2,t!=null&&(l.callback=t),t=yn(e,l,s),t!==null&&(At(t,e,s,n),io(t,e,s))}};function Cu(e,t,n,s,l,c,p){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(s,c,p):t.prototype&&t.prototype.isPureReactComponent?!Yr(n,s)||!Yr(l,c):!0}function Nu(e,t,n){var s=!1,l=hn,c=t.contextType;return typeof c=="object"&&c!==null?c=St(c):(l=lt(t)?Dn:Ze.current,s=t.contextTypes,c=(s=s!=null)?pr(e,l):hn),t=new t(n,c),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=fo,e.stateNode=t,t._reactInternals=e,s&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=l,e.__reactInternalMemoizedMaskedChildContext=c),t}function ju(e,t,n,s){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,s),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,s),t.state!==e&&fo.enqueueReplaceState(t,t.state,null)}function ma(e,t,n,s){var l=e.stateNode;l.props=n,l.state=e.memoizedState,l.refs={},ea(e);var c=t.contextType;typeof c=="object"&&c!==null?l.context=St(c):(c=lt(t)?Dn:Ze.current,l.context=pr(e,c)),l.state=e.memoizedState,c=t.getDerivedStateFromProps,typeof c=="function"&&(fa(e,t,c,n),l.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof l.getSnapshotBeforeUpdate=="function"||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(t=l.state,typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount(),t!==l.state&&fo.enqueueReplaceState(l,l.state,null),oo(e,n,l,s),l.state=e.memoizedState),typeof l.componentDidMount=="function"&&(e.flags|=4194308)}function xr(e,t){try{var n="",s=t;do n+=pe(s),s=s.return;while(s);var l=n}catch(c){l=`
Error generating stack: `+c.message+`
`+c.stack}return{value:e,source:t,stack:l,digest:null}}function ha(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function va(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Tm=typeof WeakMap=="function"?WeakMap:Map;function Ru(e,t,n){n=en(-1,n),n.tag=3,n.payload={element:null};var s=t.value;return n.callback=function(){wo||(wo=!0,Oa=s),va(e,t)},n}function Tu(e,t,n){n=en(-1,n),n.tag=3;var s=e.type.getDerivedStateFromError;if(typeof s=="function"){var l=t.value;n.payload=function(){return s(l)},n.callback=function(){va(e,t)}}var c=e.stateNode;return c!==null&&typeof c.componentDidCatch=="function"&&(n.callback=function(){va(e,t),typeof s!="function"&&(wn===null?wn=new Set([this]):wn.add(this));var p=t.stack;this.componentDidCatch(t.value,{componentStack:p!==null?p:""})}),n}function Ou(e,t,n){var s=e.pingCache;if(s===null){s=e.pingCache=new Tm;var l=new Set;s.set(t,l)}else l=s.get(t),l===void 0&&(l=new Set,s.set(t,l));l.has(n)||(l.add(n),e=Hm.bind(null,e,t,n),t.then(e,e))}function Pu(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Au(e,t,n,s,l){return(e.mode&1)===0?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=en(-1,1),t.tag=2,yn(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=l,e)}var Om=U.ReactCurrentOwner,ct=!1;function ot(e,t,n,s){t.child=e===null?Zc(t,null,n,s):hr(t,e.child,n,s)}function Du(e,t,n,s,l){n=n.render;var c=t.ref;return gr(t,l),s=aa(e,t,n,s,c,l),n=la(),e!==null&&!ct?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,tn(e,t,l)):(Ne&&n&&Hs(t),t.flags|=1,ot(e,t,s,l),t.child)}function zu(e,t,n,s,l){if(e===null){var c=n.type;return typeof c=="function"&&!Ia(c)&&c.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=c,Lu(e,t,c,s,l)):(e=Co(n.type,null,s,t,t.mode,l),e.ref=t.ref,e.return=t,t.child=e)}if(c=e.child,(e.lanes&l)===0){var p=c.memoizedProps;if(n=n.compare,n=n!==null?n:Yr,n(p,s)&&e.ref===t.ref)return tn(e,t,l)}return t.flags|=1,e=_n(c,s),e.ref=t.ref,e.return=t,t.child=e}function Lu(e,t,n,s,l){if(e!==null){var c=e.memoizedProps;if(Yr(c,s)&&e.ref===t.ref)if(ct=!1,t.pendingProps=s=c,(e.lanes&l)!==0)(e.flags&131072)!==0&&(ct=!0);else return t.lanes=e.lanes,tn(e,t,l)}return ga(e,t,n,s,l)}function Fu(e,t,n){var s=t.pendingProps,l=s.children,c=e!==null?e.memoizedState:null;if(s.mode==="hidden")if((t.mode&1)===0)t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Se(kr,yt),yt|=n;else{if((n&1073741824)===0)return e=c!==null?c.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Se(kr,yt),yt|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},s=c!==null?c.baseLanes:n,Se(kr,yt),yt|=s}else c!==null?(s=c.baseLanes|n,t.memoizedState=null):s=n,Se(kr,yt),yt|=s;return ot(e,t,l,n),t.child}function Iu(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function ga(e,t,n,s,l){var c=lt(n)?Dn:Ze.current;return c=pr(t,c),gr(t,l),n=aa(e,t,n,s,c,l),s=la(),e!==null&&!ct?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,tn(e,t,l)):(Ne&&s&&Hs(t),t.flags|=1,ot(e,t,n,l),t.child)}function Bu(e,t,n,s,l){if(lt(n)){var c=!0;Yi(t)}else c=!1;if(gr(t,l),t.stateNode===null)ho(e,t),Nu(t,n,s),ma(t,n,s,l),s=!0;else if(e===null){var p=t.stateNode,m=t.memoizedProps;p.props=m;var h=p.context,S=n.contextType;typeof S=="object"&&S!==null?S=St(S):(S=lt(n)?Dn:Ze.current,S=pr(t,S));var T=n.getDerivedStateFromProps,O=typeof T=="function"||typeof p.getSnapshotBeforeUpdate=="function";O||typeof p.UNSAFE_componentWillReceiveProps!="function"&&typeof p.componentWillReceiveProps!="function"||(m!==s||h!==S)&&ju(t,p,s,S),gn=!1;var R=t.memoizedState;p.state=R,oo(t,s,p,l),h=t.memoizedState,m!==s||R!==h||at.current||gn?(typeof T=="function"&&(fa(t,n,T,s),h=t.memoizedState),(m=gn||Cu(t,n,m,s,R,h,S))?(O||typeof p.UNSAFE_componentWillMount!="function"&&typeof p.componentWillMount!="function"||(typeof p.componentWillMount=="function"&&p.componentWillMount(),typeof p.UNSAFE_componentWillMount=="function"&&p.UNSAFE_componentWillMount()),typeof p.componentDidMount=="function"&&(t.flags|=4194308)):(typeof p.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=s,t.memoizedState=h),p.props=s,p.state=h,p.context=S,s=m):(typeof p.componentDidMount=="function"&&(t.flags|=4194308),s=!1)}else{p=t.stateNode,tu(e,t),m=t.memoizedProps,S=t.type===t.elementType?m:Tt(t.type,m),p.props=S,O=t.pendingProps,R=p.context,h=n.contextType,typeof h=="object"&&h!==null?h=St(h):(h=lt(n)?Dn:Ze.current,h=pr(t,h));var M=n.getDerivedStateFromProps;(T=typeof M=="function"||typeof p.getSnapshotBeforeUpdate=="function")||typeof p.UNSAFE_componentWillReceiveProps!="function"&&typeof p.componentWillReceiveProps!="function"||(m!==O||R!==h)&&ju(t,p,s,h),gn=!1,R=t.memoizedState,p.state=R,oo(t,s,p,l);var H=t.memoizedState;m!==O||R!==H||at.current||gn?(typeof M=="function"&&(fa(t,n,M,s),H=t.memoizedState),(S=gn||Cu(t,n,S,s,R,H,h)||!1)?(T||typeof p.UNSAFE_componentWillUpdate!="function"&&typeof p.componentWillUpdate!="function"||(typeof p.componentWillUpdate=="function"&&p.componentWillUpdate(s,H,h),typeof p.UNSAFE_componentWillUpdate=="function"&&p.UNSAFE_componentWillUpdate(s,H,h)),typeof p.componentDidUpdate=="function"&&(t.flags|=4),typeof p.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof p.componentDidUpdate!="function"||m===e.memoizedProps&&R===e.memoizedState||(t.flags|=4),typeof p.getSnapshotBeforeUpdate!="function"||m===e.memoizedProps&&R===e.memoizedState||(t.flags|=1024),t.memoizedProps=s,t.memoizedState=H),p.props=s,p.state=H,p.context=h,s=S):(typeof p.componentDidUpdate!="function"||m===e.memoizedProps&&R===e.memoizedState||(t.flags|=4),typeof p.getSnapshotBeforeUpdate!="function"||m===e.memoizedProps&&R===e.memoizedState||(t.flags|=1024),s=!1)}return ya(e,t,n,s,c,l)}function ya(e,t,n,s,l,c){Iu(e,t);var p=(t.flags&128)!==0;if(!s&&!p)return l&&Hc(t,n,!1),tn(e,t,c);s=t.stateNode,Om.current=t;var m=p&&typeof n.getDerivedStateFromError!="function"?null:s.render();return t.flags|=1,e!==null&&p?(t.child=hr(t,e.child,null,c),t.child=hr(t,null,m,c)):ot(e,t,m,c),t.memoizedState=s.state,l&&Hc(t,n,!0),t.child}function Mu(e){var t=e.stateNode;t.pendingContext?qc(e,t.pendingContext,t.pendingContext!==t.context):t.context&&qc(e,t.context,!1),ta(e,t.containerInfo)}function Uu(e,t,n,s,l){return mr(),Qs(l),t.flags|=256,ot(e,t,n,s),t.child}var xa={dehydrated:null,treeContext:null,retryLane:0};function wa(e){return{baseLanes:e,cachePool:null,transitions:null}}function qu(e,t,n){var s=t.pendingProps,l=Te.current,c=!1,p=(t.flags&128)!==0,m;if((m=p)||(m=e!==null&&e.memoizedState===null?!1:(l&2)!==0),m?(c=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(l|=1),Se(Te,l&1),e===null)return Ks(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?((t.mode&1)===0?t.lanes=1:e.data==="$!"?t.lanes=8:t.lanes=1073741824,null):(p=s.children,e=s.fallback,c?(s=t.mode,c=t.child,p={mode:"hidden",children:p},(s&1)===0&&c!==null?(c.childLanes=0,c.pendingProps=p):c=No(p,s,0,null),e=Hn(e,s,n,null),c.return=t,e.return=t,c.sibling=e,t.child=c,t.child.memoizedState=wa(n),t.memoizedState=xa,e):ka(t,p));if(l=e.memoizedState,l!==null&&(m=l.dehydrated,m!==null))return Pm(e,t,p,s,m,l,n);if(c){c=s.fallback,p=t.mode,l=e.child,m=l.sibling;var h={mode:"hidden",children:s.children};return(p&1)===0&&t.child!==l?(s=t.child,s.childLanes=0,s.pendingProps=h,t.deletions=null):(s=_n(l,h),s.subtreeFlags=l.subtreeFlags&14680064),m!==null?c=_n(m,c):(c=Hn(c,p,n,null),c.flags|=2),c.return=t,s.return=t,s.sibling=c,t.child=s,s=c,c=t.child,p=e.child.memoizedState,p=p===null?wa(n):{baseLanes:p.baseLanes|n,cachePool:null,transitions:p.transitions},c.memoizedState=p,c.childLanes=e.childLanes&~n,t.memoizedState=xa,s}return c=e.child,e=c.sibling,s=_n(c,{mode:"visible",children:s.children}),(t.mode&1)===0&&(s.lanes=n),s.return=t,s.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=s,t.memoizedState=null,s}function ka(e,t){return t=No({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function mo(e,t,n,s){return s!==null&&Qs(s),hr(t,e.child,null,n),e=ka(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Pm(e,t,n,s,l,c,p){if(n)return t.flags&256?(t.flags&=-257,s=ha(Error(o(422))),mo(e,t,p,s)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(c=s.fallback,l=t.mode,s=No({mode:"visible",children:s.children},l,0,null),c=Hn(c,l,p,null),c.flags|=2,s.return=t,c.return=t,s.sibling=c,t.child=s,(t.mode&1)!==0&&hr(t,e.child,null,p),t.child.memoizedState=wa(p),t.memoizedState=xa,c);if((t.mode&1)===0)return mo(e,t,p,null);if(l.data==="$!"){if(s=l.nextSibling&&l.nextSibling.dataset,s)var m=s.dgst;return s=m,c=Error(o(419)),s=ha(c,s,void 0),mo(e,t,p,s)}if(m=(p&e.childLanes)!==0,ct||m){if(s=Ke,s!==null){switch(p&-p){case 4:l=2;break;case 16:l=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:l=32;break;case 536870912:l=268435456;break;default:l=0}l=(l&(s.suspendedLanes|p))!==0?0:l,l!==0&&l!==c.retryLane&&(c.retryLane=l,Zt(e,l),At(s,e,l,-1))}return Fa(),s=ha(Error(o(421))),mo(e,t,p,s)}return l.data==="$?"?(t.flags|=128,t.child=e.child,t=Vm.bind(null,e),l._reactRetry=t,null):(e=c.treeContext,gt=fn(l.nextSibling),vt=t,Ne=!0,Rt=null,e!==null&&(wt[kt++]=Xt,wt[kt++]=Gt,wt[kt++]=zn,Xt=e.id,Gt=e.overflow,zn=t),t=ka(t,s.children),t.flags|=4096,t)}function $u(e,t,n){e.lanes|=t;var s=e.alternate;s!==null&&(s.lanes|=t),Gs(e.return,t,n)}function Sa(e,t,n,s,l){var c=e.memoizedState;c===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:s,tail:n,tailMode:l}:(c.isBackwards=t,c.rendering=null,c.renderingStartTime=0,c.last=s,c.tail=n,c.tailMode=l)}function Hu(e,t,n){var s=t.pendingProps,l=s.revealOrder,c=s.tail;if(ot(e,t,s.children,n),s=Te.current,(s&2)!==0)s=s&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&$u(e,n,t);else if(e.tag===19)$u(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}s&=1}if(Se(Te,s),(t.mode&1)===0)t.memoizedState=null;else switch(l){case"forwards":for(n=t.child,l=null;n!==null;)e=n.alternate,e!==null&&so(e)===null&&(l=n),n=n.sibling;n=l,n===null?(l=t.child,t.child=null):(l=n.sibling,n.sibling=null),Sa(t,!1,l,n,c);break;case"backwards":for(n=null,l=t.child,t.child=null;l!==null;){if(e=l.alternate,e!==null&&so(e)===null){t.child=l;break}e=l.sibling,l.sibling=n,n=l,l=e}Sa(t,!0,n,null,c);break;case"together":Sa(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function ho(e,t){(t.mode&1)===0&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function tn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Mn|=t.lanes,(n&t.childLanes)===0)return null;if(e!==null&&t.child!==e.child)throw Error(o(153));if(t.child!==null){for(e=t.child,n=_n(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=_n(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Am(e,t,n){switch(t.tag){case 3:Mu(t),mr();break;case 5:iu(t);break;case 1:lt(t.type)&&Yi(t);break;case 4:ta(t,t.stateNode.containerInfo);break;case 10:var s=t.type._context,l=t.memoizedProps.value;Se(no,s._currentValue),s._currentValue=l;break;case 13:if(s=t.memoizedState,s!==null)return s.dehydrated!==null?(Se(Te,Te.current&1),t.flags|=128,null):(n&t.child.childLanes)!==0?qu(e,t,n):(Se(Te,Te.current&1),e=tn(e,t,n),e!==null?e.sibling:null);Se(Te,Te.current&1);break;case 19:if(s=(n&t.childLanes)!==0,(e.flags&128)!==0){if(s)return Hu(e,t,n);t.flags|=128}if(l=t.memoizedState,l!==null&&(l.rendering=null,l.tail=null,l.lastEffect=null),Se(Te,Te.current),s)break;return null;case 22:case 23:return t.lanes=0,Fu(e,t,n)}return tn(e,t,n)}var Vu,Ea,Wu,Ku;Vu=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Ea=function(){},Wu=function(e,t,n,s){var l=e.memoizedProps;if(l!==s){e=t.stateNode,In(Mt.current);var c=null;switch(n){case"input":l=Xn(e,l),s=Xn(e,s),c=[];break;case"select":l=q({},l,{value:void 0}),s=q({},s,{value:void 0}),c=[];break;case"textarea":l=Or(e,l),s=Or(e,s),c=[];break;default:typeof l.onClick!="function"&&typeof s.onClick=="function"&&(e.onclick=Ki)}Rn(n,s);var p;n=null;for(S in l)if(!s.hasOwnProperty(S)&&l.hasOwnProperty(S)&&l[S]!=null)if(S==="style"){var m=l[S];for(p in m)m.hasOwnProperty(p)&&(n||(n={}),n[p]="")}else S!=="dangerouslySetInnerHTML"&&S!=="children"&&S!=="suppressContentEditableWarning"&&S!=="suppressHydrationWarning"&&S!=="autoFocus"&&(u.hasOwnProperty(S)?c||(c=[]):(c=c||[]).push(S,null));for(S in s){var h=s[S];if(m=l!=null?l[S]:void 0,s.hasOwnProperty(S)&&h!==m&&(h!=null||m!=null))if(S==="style")if(m){for(p in m)!m.hasOwnProperty(p)||h&&h.hasOwnProperty(p)||(n||(n={}),n[p]="");for(p in h)h.hasOwnProperty(p)&&m[p]!==h[p]&&(n||(n={}),n[p]=h[p])}else n||(c||(c=[]),c.push(S,n)),n=h;else S==="dangerouslySetInnerHTML"?(h=h?h.__html:void 0,m=m?m.__html:void 0,h!=null&&m!==h&&(c=c||[]).push(S,h)):S==="children"?typeof h!="string"&&typeof h!="number"||(c=c||[]).push(S,""+h):S!=="suppressContentEditableWarning"&&S!=="suppressHydrationWarning"&&(u.hasOwnProperty(S)?(h!=null&&S==="onScroll"&&be("scroll",e),c||m===h||(c=[])):(c=c||[]).push(S,h))}n&&(c=c||[]).push("style",n);var S=c;(t.updateQueue=S)&&(t.flags|=4)}},Ku=function(e,t,n,s){n!==s&&(t.flags|=4)};function pi(e,t){if(!Ne)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var s=null;n!==null;)n.alternate!==null&&(s=n),n=n.sibling;s===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:s.sibling=null}}function tt(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,s=0;if(t)for(var l=e.child;l!==null;)n|=l.lanes|l.childLanes,s|=l.subtreeFlags&14680064,s|=l.flags&14680064,l.return=e,l=l.sibling;else for(l=e.child;l!==null;)n|=l.lanes|l.childLanes,s|=l.subtreeFlags,s|=l.flags,l.return=e,l=l.sibling;return e.subtreeFlags|=s,e.childLanes=n,t}function Dm(e,t,n){var s=t.pendingProps;switch(Vs(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return tt(t),null;case 1:return lt(t.type)&&Ji(),tt(t),null;case 3:return s=t.stateNode,yr(),Ce(at),Ce(Ze),ia(),s.pendingContext&&(s.context=s.pendingContext,s.pendingContext=null),(e===null||e.child===null)&&(eo(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,Rt!==null&&(Da(Rt),Rt=null))),Ea(e,t),tt(t),null;case 5:na(t);var l=In(si.current);if(n=t.type,e!==null&&t.stateNode!=null)Wu(e,t,n,s,l),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!s){if(t.stateNode===null)throw Error(o(166));return tt(t),null}if(e=In(Mt.current),eo(t)){s=t.stateNode,n=t.type;var c=t.memoizedProps;switch(s[Bt]=t,s[ti]=c,e=(t.mode&1)!==0,n){case"dialog":be("cancel",s),be("close",s);break;case"iframe":case"object":case"embed":be("load",s);break;case"video":case"audio":for(l=0;l<Gr.length;l++)be(Gr[l],s);break;case"source":be("error",s);break;case"img":case"image":case"link":be("error",s),be("load",s);break;case"details":be("toggle",s);break;case"input":Rr(s,c),be("invalid",s);break;case"select":s._wrapperState={wasMultiple:!!c.multiple},be("invalid",s);break;case"textarea":bi(s,c),be("invalid",s)}Rn(n,c),l=null;for(var p in c)if(c.hasOwnProperty(p)){var m=c[p];p==="children"?typeof m=="string"?s.textContent!==m&&(c.suppressHydrationWarning!==!0&&Wi(s.textContent,m,e),l=["children",m]):typeof m=="number"&&s.textContent!==""+m&&(c.suppressHydrationWarning!==!0&&Wi(s.textContent,m,e),l=["children",""+m]):u.hasOwnProperty(p)&&m!=null&&p==="onScroll"&&be("scroll",s)}switch(n){case"input":Wt(s),Gn(s,c,!0);break;case"textarea":Wt(s),G(s);break;case"select":case"option":break;default:typeof c.onClick=="function"&&(s.onclick=Ki)}s=l,t.updateQueue=s,s!==null&&(t.flags|=4)}else{p=l.nodeType===9?l:l.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Re(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=p.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof s.is=="string"?e=p.createElement(n,{is:s.is}):(e=p.createElement(n),n==="select"&&(p=e,s.multiple?p.multiple=!0:s.size&&(p.size=s.size))):e=p.createElementNS(e,n),e[Bt]=t,e[ti]=s,Vu(e,t,!1,!1),t.stateNode=e;e:{switch(p=Tn(n,s),n){case"dialog":be("cancel",e),be("close",e),l=s;break;case"iframe":case"object":case"embed":be("load",e),l=s;break;case"video":case"audio":for(l=0;l<Gr.length;l++)be(Gr[l],e);l=s;break;case"source":be("error",e),l=s;break;case"img":case"image":case"link":be("error",e),be("load",e),l=s;break;case"details":be("toggle",e),l=s;break;case"input":Rr(e,s),l=Xn(e,s),be("invalid",e);break;case"option":l=s;break;case"select":e._wrapperState={wasMultiple:!!s.multiple},l=q({},s,{value:void 0}),be("invalid",e);break;case"textarea":bi(e,s),l=Or(e,s),be("invalid",e);break;default:l=s}Rn(n,l),m=l;for(c in m)if(m.hasOwnProperty(c)){var h=m[c];c==="style"?jn(e,h):c==="dangerouslySetInnerHTML"?(h=h?h.__html:void 0,h!=null&&sn(e,h)):c==="children"?typeof h=="string"?(n!=="textarea"||h!=="")&&xt(e,h):typeof h=="number"&&xt(e,""+h):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(u.hasOwnProperty(c)?h!=null&&c==="onScroll"&&be("scroll",e):h!=null&&B(e,c,h,p))}switch(n){case"input":Wt(e),Gn(e,s,!1);break;case"textarea":Wt(e),G(e);break;case"option":s.value!=null&&e.setAttribute("value",""+me(s.value));break;case"select":e.multiple=!!s.multiple,c=s.value,c!=null?on(e,!!s.multiple,c,!1):s.defaultValue!=null&&on(e,!!s.multiple,s.defaultValue,!0);break;default:typeof l.onClick=="function"&&(e.onclick=Ki)}switch(n){case"button":case"input":case"select":case"textarea":s=!!s.autoFocus;break e;case"img":s=!0;break e;default:s=!1}}s&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return tt(t),null;case 6:if(e&&t.stateNode!=null)Ku(e,t,e.memoizedProps,s);else{if(typeof s!="string"&&t.stateNode===null)throw Error(o(166));if(n=In(si.current),In(Mt.current),eo(t)){if(s=t.stateNode,n=t.memoizedProps,s[Bt]=t,(c=s.nodeValue!==n)&&(e=vt,e!==null))switch(e.tag){case 3:Wi(s.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Wi(s.nodeValue,n,(e.mode&1)!==0)}c&&(t.flags|=4)}else s=(n.nodeType===9?n:n.ownerDocument).createTextNode(s),s[Bt]=t,t.stateNode=s}return tt(t),null;case 13:if(Ce(Te),s=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(Ne&&gt!==null&&(t.mode&1)!==0&&(t.flags&128)===0)Yc(),mr(),t.flags|=98560,c=!1;else if(c=eo(t),s!==null&&s.dehydrated!==null){if(e===null){if(!c)throw Error(o(318));if(c=t.memoizedState,c=c!==null?c.dehydrated:null,!c)throw Error(o(317));c[Bt]=t}else mr(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;tt(t),c=!1}else Rt!==null&&(Da(Rt),Rt=null),c=!0;if(!c)return t.flags&65536?t:null}return(t.flags&128)!==0?(t.lanes=n,t):(s=s!==null,s!==(e!==null&&e.memoizedState!==null)&&s&&(t.child.flags|=8192,(t.mode&1)!==0&&(e===null||(Te.current&1)!==0?$e===0&&($e=3):Fa())),t.updateQueue!==null&&(t.flags|=4),tt(t),null);case 4:return yr(),Ea(e,t),e===null&&Zr(t.stateNode.containerInfo),tt(t),null;case 10:return Xs(t.type._context),tt(t),null;case 17:return lt(t.type)&&Ji(),tt(t),null;case 19:if(Ce(Te),c=t.memoizedState,c===null)return tt(t),null;if(s=(t.flags&128)!==0,p=c.rendering,p===null)if(s)pi(c,!1);else{if($e!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(p=so(e),p!==null){for(t.flags|=128,pi(c,!1),s=p.updateQueue,s!==null&&(t.updateQueue=s,t.flags|=4),t.subtreeFlags=0,s=n,n=t.child;n!==null;)c=n,e=s,c.flags&=14680066,p=c.alternate,p===null?(c.childLanes=0,c.lanes=e,c.child=null,c.subtreeFlags=0,c.memoizedProps=null,c.memoizedState=null,c.updateQueue=null,c.dependencies=null,c.stateNode=null):(c.childLanes=p.childLanes,c.lanes=p.lanes,c.child=p.child,c.subtreeFlags=0,c.deletions=null,c.memoizedProps=p.memoizedProps,c.memoizedState=p.memoizedState,c.updateQueue=p.updateQueue,c.type=p.type,e=p.dependencies,c.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Se(Te,Te.current&1|2),t.child}e=e.sibling}c.tail!==null&&Le()>Sr&&(t.flags|=128,s=!0,pi(c,!1),t.lanes=4194304)}else{if(!s)if(e=so(p),e!==null){if(t.flags|=128,s=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),pi(c,!0),c.tail===null&&c.tailMode==="hidden"&&!p.alternate&&!Ne)return tt(t),null}else 2*Le()-c.renderingStartTime>Sr&&n!==1073741824&&(t.flags|=128,s=!0,pi(c,!1),t.lanes=4194304);c.isBackwards?(p.sibling=t.child,t.child=p):(n=c.last,n!==null?n.sibling=p:t.child=p,c.last=p)}return c.tail!==null?(t=c.tail,c.rendering=t,c.tail=t.sibling,c.renderingStartTime=Le(),t.sibling=null,n=Te.current,Se(Te,s?n&1|2:n&1),t):(tt(t),null);case 22:case 23:return La(),s=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==s&&(t.flags|=8192),s&&(t.mode&1)!==0?(yt&1073741824)!==0&&(tt(t),t.subtreeFlags&6&&(t.flags|=8192)):tt(t),null;case 24:return null;case 25:return null}throw Error(o(156,t.tag))}function zm(e,t){switch(Vs(t),t.tag){case 1:return lt(t.type)&&Ji(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return yr(),Ce(at),Ce(Ze),ia(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 5:return na(t),null;case 13:if(Ce(Te),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(o(340));mr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return Ce(Te),null;case 4:return yr(),null;case 10:return Xs(t.type._context),null;case 22:case 23:return La(),null;case 24:return null;default:return null}}var vo=!1,nt=!1,Lm=typeof WeakSet=="function"?WeakSet:Set,$=null;function wr(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(s){De(e,t,s)}else n.current=null}function _a(e,t,n){try{n()}catch(s){De(e,t,s)}}var Qu=!1;function Fm(e,t){if(Ls=zi,e=Cc(),js(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var s=n.getSelection&&n.getSelection();if(s&&s.rangeCount!==0){n=s.anchorNode;var l=s.anchorOffset,c=s.focusNode;s=s.focusOffset;try{n.nodeType,c.nodeType}catch{n=null;break e}var p=0,m=-1,h=-1,S=0,T=0,O=e,R=null;t:for(;;){for(var M;O!==n||l!==0&&O.nodeType!==3||(m=p+l),O!==c||s!==0&&O.nodeType!==3||(h=p+s),O.nodeType===3&&(p+=O.nodeValue.length),(M=O.firstChild)!==null;)R=O,O=M;for(;;){if(O===e)break t;if(R===n&&++S===l&&(m=p),R===c&&++T===s&&(h=p),(M=O.nextSibling)!==null)break;O=R,R=O.parentNode}O=M}n=m===-1||h===-1?null:{start:m,end:h}}else n=null}n=n||{start:0,end:0}}else n=null;for(Fs={focusedElem:e,selectionRange:n},zi=!1,$=t;$!==null;)if(t=$,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,$=e;else for(;$!==null;){t=$;try{var H=t.alternate;if((t.flags&1024)!==0)switch(t.tag){case 0:case 11:case 15:break;case 1:if(H!==null){var W=H.memoizedProps,Fe=H.memoizedState,w=t.stateNode,y=w.getSnapshotBeforeUpdate(t.elementType===t.type?W:Tt(t.type,W),Fe);w.__reactInternalSnapshotBeforeUpdate=y}break;case 3:var k=t.stateNode.containerInfo;k.nodeType===1?k.textContent="":k.nodeType===9&&k.documentElement&&k.removeChild(k.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(o(163))}}catch(A){De(t,t.return,A)}if(e=t.sibling,e!==null){e.return=t.return,$=e;break}$=t.return}return H=Qu,Qu=!1,H}function di(e,t,n){var s=t.updateQueue;if(s=s!==null?s.lastEffect:null,s!==null){var l=s=s.next;do{if((l.tag&e)===e){var c=l.destroy;l.destroy=void 0,c!==void 0&&_a(t,n,c)}l=l.next}while(l!==s)}}function go(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var s=n.create;n.destroy=s()}n=n.next}while(n!==t)}}function ba(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Ju(e){var t=e.alternate;t!==null&&(e.alternate=null,Ju(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Bt],delete t[ti],delete t[Us],delete t[xm],delete t[wm])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Yu(e){return e.tag===5||e.tag===3||e.tag===4}function Xu(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Yu(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Ca(e,t,n){var s=e.tag;if(s===5||s===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Ki));else if(s!==4&&(e=e.child,e!==null))for(Ca(e,t,n),e=e.sibling;e!==null;)Ca(e,t,n),e=e.sibling}function Na(e,t,n){var s=e.tag;if(s===5||s===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(s!==4&&(e=e.child,e!==null))for(Na(e,t,n),e=e.sibling;e!==null;)Na(e,t,n),e=e.sibling}var Ye=null,Ot=!1;function xn(e,t,n){for(n=n.child;n!==null;)Gu(e,t,n),n=n.sibling}function Gu(e,t,n){if(It&&typeof It.onCommitFiberUnmount=="function")try{It.onCommitFiberUnmount(Ri,n)}catch{}switch(n.tag){case 5:nt||wr(n,t);case 6:var s=Ye,l=Ot;Ye=null,xn(e,t,n),Ye=s,Ot=l,Ye!==null&&(Ot?(e=Ye,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Ye.removeChild(n.stateNode));break;case 18:Ye!==null&&(Ot?(e=Ye,n=n.stateNode,e.nodeType===8?Ms(e.parentNode,n):e.nodeType===1&&Ms(e,n),Hr(e)):Ms(Ye,n.stateNode));break;case 4:s=Ye,l=Ot,Ye=n.stateNode.containerInfo,Ot=!0,xn(e,t,n),Ye=s,Ot=l;break;case 0:case 11:case 14:case 15:if(!nt&&(s=n.updateQueue,s!==null&&(s=s.lastEffect,s!==null))){l=s=s.next;do{var c=l,p=c.destroy;c=c.tag,p!==void 0&&((c&2)!==0||(c&4)!==0)&&_a(n,t,p),l=l.next}while(l!==s)}xn(e,t,n);break;case 1:if(!nt&&(wr(n,t),s=n.stateNode,typeof s.componentWillUnmount=="function"))try{s.props=n.memoizedProps,s.state=n.memoizedState,s.componentWillUnmount()}catch(m){De(n,t,m)}xn(e,t,n);break;case 21:xn(e,t,n);break;case 22:n.mode&1?(nt=(s=nt)||n.memoizedState!==null,xn(e,t,n),nt=s):xn(e,t,n);break;default:xn(e,t,n)}}function Zu(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Lm),t.forEach(function(s){var l=Wm.bind(null,e,s);n.has(s)||(n.add(s),s.then(l,l))})}}function Pt(e,t){var n=t.deletions;if(n!==null)for(var s=0;s<n.length;s++){var l=n[s];try{var c=e,p=t,m=p;e:for(;m!==null;){switch(m.tag){case 5:Ye=m.stateNode,Ot=!1;break e;case 3:Ye=m.stateNode.containerInfo,Ot=!0;break e;case 4:Ye=m.stateNode.containerInfo,Ot=!0;break e}m=m.return}if(Ye===null)throw Error(o(160));Gu(c,p,l),Ye=null,Ot=!1;var h=l.alternate;h!==null&&(h.return=null),l.return=null}catch(S){De(l,t,S)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)ep(t,e),t=t.sibling}function ep(e,t){var n=e.alternate,s=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Pt(t,e),qt(e),s&4){try{di(3,e,e.return),go(3,e)}catch(W){De(e,e.return,W)}try{di(5,e,e.return)}catch(W){De(e,e.return,W)}}break;case 1:Pt(t,e),qt(e),s&512&&n!==null&&wr(n,n.return);break;case 5:if(Pt(t,e),qt(e),s&512&&n!==null&&wr(n,n.return),e.flags&32){var l=e.stateNode;try{xt(l,"")}catch(W){De(e,e.return,W)}}if(s&4&&(l=e.stateNode,l!=null)){var c=e.memoizedProps,p=n!==null?n.memoizedProps:c,m=e.type,h=e.updateQueue;if(e.updateQueue=null,h!==null)try{m==="input"&&c.type==="radio"&&c.name!=null&&Tr(l,c),Tn(m,p);var S=Tn(m,c);for(p=0;p<h.length;p+=2){var T=h[p],O=h[p+1];T==="style"?jn(l,O):T==="dangerouslySetInnerHTML"?sn(l,O):T==="children"?xt(l,O):B(l,T,O,S)}switch(m){case"input":Lt(l,c);break;case"textarea":I(l,c);break;case"select":var R=l._wrapperState.wasMultiple;l._wrapperState.wasMultiple=!!c.multiple;var M=c.value;M!=null?on(l,!!c.multiple,M,!1):R!==!!c.multiple&&(c.defaultValue!=null?on(l,!!c.multiple,c.defaultValue,!0):on(l,!!c.multiple,c.multiple?[]:"",!1))}l[ti]=c}catch(W){De(e,e.return,W)}}break;case 6:if(Pt(t,e),qt(e),s&4){if(e.stateNode===null)throw Error(o(162));l=e.stateNode,c=e.memoizedProps;try{l.nodeValue=c}catch(W){De(e,e.return,W)}}break;case 3:if(Pt(t,e),qt(e),s&4&&n!==null&&n.memoizedState.isDehydrated)try{Hr(t.containerInfo)}catch(W){De(e,e.return,W)}break;case 4:Pt(t,e),qt(e);break;case 13:Pt(t,e),qt(e),l=e.child,l.flags&8192&&(c=l.memoizedState!==null,l.stateNode.isHidden=c,!c||l.alternate!==null&&l.alternate.memoizedState!==null||(Ta=Le())),s&4&&Zu(e);break;case 22:if(T=n!==null&&n.memoizedState!==null,e.mode&1?(nt=(S=nt)||T,Pt(t,e),nt=S):Pt(t,e),qt(e),s&8192){if(S=e.memoizedState!==null,(e.stateNode.isHidden=S)&&!T&&(e.mode&1)!==0)for($=e,T=e.child;T!==null;){for(O=$=T;$!==null;){switch(R=$,M=R.child,R.tag){case 0:case 11:case 14:case 15:di(4,R,R.return);break;case 1:wr(R,R.return);var H=R.stateNode;if(typeof H.componentWillUnmount=="function"){s=R,n=R.return;try{t=s,H.props=t.memoizedProps,H.state=t.memoizedState,H.componentWillUnmount()}catch(W){De(s,n,W)}}break;case 5:wr(R,R.return);break;case 22:if(R.memoizedState!==null){rp(O);continue}}M!==null?(M.return=R,$=M):rp(O)}T=T.sibling}e:for(T=null,O=e;;){if(O.tag===5){if(T===null){T=O;try{l=O.stateNode,S?(c=l.style,typeof c.setProperty=="function"?c.setProperty("display","none","important"):c.display="none"):(m=O.stateNode,h=O.memoizedProps.style,p=h!=null&&h.hasOwnProperty("display")?h.display:null,m.style.display=Zn("display",p))}catch(W){De(e,e.return,W)}}}else if(O.tag===6){if(T===null)try{O.stateNode.nodeValue=S?"":O.memoizedProps}catch(W){De(e,e.return,W)}}else if((O.tag!==22&&O.tag!==23||O.memoizedState===null||O===e)&&O.child!==null){O.child.return=O,O=O.child;continue}if(O===e)break e;for(;O.sibling===null;){if(O.return===null||O.return===e)break e;T===O&&(T=null),O=O.return}T===O&&(T=null),O.sibling.return=O.return,O=O.sibling}}break;case 19:Pt(t,e),qt(e),s&4&&Zu(e);break;case 21:break;default:Pt(t,e),qt(e)}}function qt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Yu(n)){var s=n;break e}n=n.return}throw Error(o(160))}switch(s.tag){case 5:var l=s.stateNode;s.flags&32&&(xt(l,""),s.flags&=-33);var c=Xu(e);Na(e,c,l);break;case 3:case 4:var p=s.stateNode.containerInfo,m=Xu(e);Ca(e,m,p);break;default:throw Error(o(161))}}catch(h){De(e,e.return,h)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Im(e,t,n){$=e,tp(e)}function tp(e,t,n){for(var s=(e.mode&1)!==0;$!==null;){var l=$,c=l.child;if(l.tag===22&&s){var p=l.memoizedState!==null||vo;if(!p){var m=l.alternate,h=m!==null&&m.memoizedState!==null||nt;m=vo;var S=nt;if(vo=p,(nt=h)&&!S)for($=l;$!==null;)p=$,h=p.child,p.tag===22&&p.memoizedState!==null?ip(l):h!==null?(h.return=p,$=h):ip(l);for(;c!==null;)$=c,tp(c),c=c.sibling;$=l,vo=m,nt=S}np(e)}else(l.subtreeFlags&8772)!==0&&c!==null?(c.return=l,$=c):np(e)}}function np(e){for(;$!==null;){var t=$;if((t.flags&8772)!==0){var n=t.alternate;try{if((t.flags&8772)!==0)switch(t.tag){case 0:case 11:case 15:nt||go(5,t);break;case 1:var s=t.stateNode;if(t.flags&4&&!nt)if(n===null)s.componentDidMount();else{var l=t.elementType===t.type?n.memoizedProps:Tt(t.type,n.memoizedProps);s.componentDidUpdate(l,n.memoizedState,s.__reactInternalSnapshotBeforeUpdate)}var c=t.updateQueue;c!==null&&ru(t,c,s);break;case 3:var p=t.updateQueue;if(p!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}ru(t,p,n)}break;case 5:var m=t.stateNode;if(n===null&&t.flags&4){n=m;var h=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":h.autoFocus&&n.focus();break;case"img":h.src&&(n.src=h.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var S=t.alternate;if(S!==null){var T=S.memoizedState;if(T!==null){var O=T.dehydrated;O!==null&&Hr(O)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(o(163))}nt||t.flags&512&&ba(t)}catch(R){De(t,t.return,R)}}if(t===e){$=null;break}if(n=t.sibling,n!==null){n.return=t.return,$=n;break}$=t.return}}function rp(e){for(;$!==null;){var t=$;if(t===e){$=null;break}var n=t.sibling;if(n!==null){n.return=t.return,$=n;break}$=t.return}}function ip(e){for(;$!==null;){var t=$;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{go(4,t)}catch(h){De(t,n,h)}break;case 1:var s=t.stateNode;if(typeof s.componentDidMount=="function"){var l=t.return;try{s.componentDidMount()}catch(h){De(t,l,h)}}var c=t.return;try{ba(t)}catch(h){De(t,c,h)}break;case 5:var p=t.return;try{ba(t)}catch(h){De(t,p,h)}}}catch(h){De(t,t.return,h)}if(t===e){$=null;break}var m=t.sibling;if(m!==null){m.return=t.return,$=m;break}$=t.return}}var Bm=Math.ceil,yo=U.ReactCurrentDispatcher,ja=U.ReactCurrentOwner,_t=U.ReactCurrentBatchConfig,fe=0,Ke=null,Me=null,Xe=0,yt=0,kr=mn(0),$e=0,fi=null,Mn=0,xo=0,Ra=0,mi=null,ut=null,Ta=0,Sr=1/0,nn=null,wo=!1,Oa=null,wn=null,ko=!1,kn=null,So=0,hi=0,Pa=null,Eo=-1,_o=0;function st(){return(fe&6)!==0?Le():Eo!==-1?Eo:Eo=Le()}function Sn(e){return(e.mode&1)===0?1:(fe&2)!==0&&Xe!==0?Xe&-Xe:Sm.transition!==null?(_o===0&&(_o=Xl()),_o):(e=ge,e!==0||(e=window.event,e=e===void 0?16:sc(e.type)),e)}function At(e,t,n,s){if(50<hi)throw hi=0,Pa=null,Error(o(185));Br(e,n,s),((fe&2)===0||e!==Ke)&&(e===Ke&&((fe&2)===0&&(xo|=n),$e===4&&En(e,Xe)),pt(e,s),n===1&&fe===0&&(t.mode&1)===0&&(Sr=Le()+500,Xi&&vn()))}function pt(e,t){var n=e.callbackNode;Sf(e,t);var s=Pi(e,e===Ke?Xe:0);if(s===0)n!==null&&Ql(n),e.callbackNode=null,e.callbackPriority=0;else if(t=s&-s,e.callbackPriority!==t){if(n!=null&&Ql(n),t===1)e.tag===0?km(sp.bind(null,e)):Vc(sp.bind(null,e)),gm(function(){(fe&6)===0&&vn()}),n=null;else{switch(Gl(s)){case 1:n=ps;break;case 4:n=Jl;break;case 16:n=ji;break;case 536870912:n=Yl;break;default:n=ji}n=mp(n,op.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function op(e,t){if(Eo=-1,_o=0,(fe&6)!==0)throw Error(o(327));var n=e.callbackNode;if(Er()&&e.callbackNode!==n)return null;var s=Pi(e,e===Ke?Xe:0);if(s===0)return null;if((s&30)!==0||(s&e.expiredLanes)!==0||t)t=bo(e,s);else{t=s;var l=fe;fe|=2;var c=lp();(Ke!==e||Xe!==t)&&(nn=null,Sr=Le()+500,qn(e,t));do try{qm();break}catch(m){ap(e,m)}while(!0);Ys(),yo.current=c,fe=l,Me!==null?t=0:(Ke=null,Xe=0,t=$e)}if(t!==0){if(t===2&&(l=ds(e),l!==0&&(s=l,t=Aa(e,l))),t===1)throw n=fi,qn(e,0),En(e,s),pt(e,Le()),n;if(t===6)En(e,s);else{if(l=e.current.alternate,(s&30)===0&&!Mm(l)&&(t=bo(e,s),t===2&&(c=ds(e),c!==0&&(s=c,t=Aa(e,c))),t===1))throw n=fi,qn(e,0),En(e,s),pt(e,Le()),n;switch(e.finishedWork=l,e.finishedLanes=s,t){case 0:case 1:throw Error(o(345));case 2:$n(e,ut,nn);break;case 3:if(En(e,s),(s&130023424)===s&&(t=Ta+500-Le(),10<t)){if(Pi(e,0)!==0)break;if(l=e.suspendedLanes,(l&s)!==s){st(),e.pingedLanes|=e.suspendedLanes&l;break}e.timeoutHandle=Bs($n.bind(null,e,ut,nn),t);break}$n(e,ut,nn);break;case 4:if(En(e,s),(s&4194240)===s)break;for(t=e.eventTimes,l=-1;0<s;){var p=31-Nt(s);c=1<<p,p=t[p],p>l&&(l=p),s&=~c}if(s=l,s=Le()-s,s=(120>s?120:480>s?480:1080>s?1080:1920>s?1920:3e3>s?3e3:4320>s?4320:1960*Bm(s/1960))-s,10<s){e.timeoutHandle=Bs($n.bind(null,e,ut,nn),s);break}$n(e,ut,nn);break;case 5:$n(e,ut,nn);break;default:throw Error(o(329))}}}return pt(e,Le()),e.callbackNode===n?op.bind(null,e):null}function Aa(e,t){var n=mi;return e.current.memoizedState.isDehydrated&&(qn(e,t).flags|=256),e=bo(e,t),e!==2&&(t=ut,ut=n,t!==null&&Da(t)),e}function Da(e){ut===null?ut=e:ut.push.apply(ut,e)}function Mm(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var s=0;s<n.length;s++){var l=n[s],c=l.getSnapshot;l=l.value;try{if(!jt(c(),l))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function En(e,t){for(t&=~Ra,t&=~xo,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Nt(t),s=1<<n;e[n]=-1,t&=~s}}function sp(e){if((fe&6)!==0)throw Error(o(327));Er();var t=Pi(e,0);if((t&1)===0)return pt(e,Le()),null;var n=bo(e,t);if(e.tag!==0&&n===2){var s=ds(e);s!==0&&(t=s,n=Aa(e,s))}if(n===1)throw n=fi,qn(e,0),En(e,t),pt(e,Le()),n;if(n===6)throw Error(o(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,$n(e,ut,nn),pt(e,Le()),null}function za(e,t){var n=fe;fe|=1;try{return e(t)}finally{fe=n,fe===0&&(Sr=Le()+500,Xi&&vn())}}function Un(e){kn!==null&&kn.tag===0&&(fe&6)===0&&Er();var t=fe;fe|=1;var n=_t.transition,s=ge;try{if(_t.transition=null,ge=1,e)return e()}finally{ge=s,_t.transition=n,fe=t,(fe&6)===0&&vn()}}function La(){yt=kr.current,Ce(kr)}function qn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,vm(n)),Me!==null)for(n=Me.return;n!==null;){var s=n;switch(Vs(s),s.tag){case 1:s=s.type.childContextTypes,s!=null&&Ji();break;case 3:yr(),Ce(at),Ce(Ze),ia();break;case 5:na(s);break;case 4:yr();break;case 13:Ce(Te);break;case 19:Ce(Te);break;case 10:Xs(s.type._context);break;case 22:case 23:La()}n=n.return}if(Ke=e,Me=e=_n(e.current,null),Xe=yt=t,$e=0,fi=null,Ra=xo=Mn=0,ut=mi=null,Fn!==null){for(t=0;t<Fn.length;t++)if(n=Fn[t],s=n.interleaved,s!==null){n.interleaved=null;var l=s.next,c=n.pending;if(c!==null){var p=c.next;c.next=l,s.next=p}n.pending=s}Fn=null}return e}function ap(e,t){do{var n=Me;try{if(Ys(),ao.current=po,lo){for(var s=Oe.memoizedState;s!==null;){var l=s.queue;l!==null&&(l.pending=null),s=s.next}lo=!1}if(Bn=0,We=qe=Oe=null,ai=!1,li=0,ja.current=null,n===null||n.return===null){$e=1,fi=t,Me=null;break}e:{var c=e,p=n.return,m=n,h=t;if(t=Xe,m.flags|=32768,h!==null&&typeof h=="object"&&typeof h.then=="function"){var S=h,T=m,O=T.tag;if((T.mode&1)===0&&(O===0||O===11||O===15)){var R=T.alternate;R?(T.updateQueue=R.updateQueue,T.memoizedState=R.memoizedState,T.lanes=R.lanes):(T.updateQueue=null,T.memoizedState=null)}var M=Pu(p);if(M!==null){M.flags&=-257,Au(M,p,m,c,t),M.mode&1&&Ou(c,S,t),t=M,h=S;var H=t.updateQueue;if(H===null){var W=new Set;W.add(h),t.updateQueue=W}else H.add(h);break e}else{if((t&1)===0){Ou(c,S,t),Fa();break e}h=Error(o(426))}}else if(Ne&&m.mode&1){var Fe=Pu(p);if(Fe!==null){(Fe.flags&65536)===0&&(Fe.flags|=256),Au(Fe,p,m,c,t),Qs(xr(h,m));break e}}c=h=xr(h,m),$e!==4&&($e=2),mi===null?mi=[c]:mi.push(c),c=p;do{switch(c.tag){case 3:c.flags|=65536,t&=-t,c.lanes|=t;var w=Ru(c,h,t);nu(c,w);break e;case 1:m=h;var y=c.type,k=c.stateNode;if((c.flags&128)===0&&(typeof y.getDerivedStateFromError=="function"||k!==null&&typeof k.componentDidCatch=="function"&&(wn===null||!wn.has(k)))){c.flags|=65536,t&=-t,c.lanes|=t;var A=Tu(c,m,t);nu(c,A);break e}}c=c.return}while(c!==null)}up(n)}catch(K){t=K,Me===n&&n!==null&&(Me=n=n.return);continue}break}while(!0)}function lp(){var e=yo.current;return yo.current=po,e===null?po:e}function Fa(){($e===0||$e===3||$e===2)&&($e=4),Ke===null||(Mn&268435455)===0&&(xo&268435455)===0||En(Ke,Xe)}function bo(e,t){var n=fe;fe|=2;var s=lp();(Ke!==e||Xe!==t)&&(nn=null,qn(e,t));do try{Um();break}catch(l){ap(e,l)}while(!0);if(Ys(),fe=n,yo.current=s,Me!==null)throw Error(o(261));return Ke=null,Xe=0,$e}function Um(){for(;Me!==null;)cp(Me)}function qm(){for(;Me!==null&&!ff();)cp(Me)}function cp(e){var t=fp(e.alternate,e,yt);e.memoizedProps=e.pendingProps,t===null?up(e):Me=t,ja.current=null}function up(e){var t=e;do{var n=t.alternate;if(e=t.return,(t.flags&32768)===0){if(n=Dm(n,t,yt),n!==null){Me=n;return}}else{if(n=zm(n,t),n!==null){n.flags&=32767,Me=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{$e=6,Me=null;return}}if(t=t.sibling,t!==null){Me=t;return}Me=t=e}while(t!==null);$e===0&&($e=5)}function $n(e,t,n){var s=ge,l=_t.transition;try{_t.transition=null,ge=1,$m(e,t,n,s)}finally{_t.transition=l,ge=s}return null}function $m(e,t,n,s){do Er();while(kn!==null);if((fe&6)!==0)throw Error(o(327));n=e.finishedWork;var l=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(o(177));e.callbackNode=null,e.callbackPriority=0;var c=n.lanes|n.childLanes;if(Ef(e,c),e===Ke&&(Me=Ke=null,Xe=0),(n.subtreeFlags&2064)===0&&(n.flags&2064)===0||ko||(ko=!0,mp(ji,function(){return Er(),null})),c=(n.flags&15990)!==0,(n.subtreeFlags&15990)!==0||c){c=_t.transition,_t.transition=null;var p=ge;ge=1;var m=fe;fe|=4,ja.current=null,Fm(e,n),ep(n,e),cm(Fs),zi=!!Ls,Fs=Ls=null,e.current=n,Im(n),mf(),fe=m,ge=p,_t.transition=c}else e.current=n;if(ko&&(ko=!1,kn=e,So=l),c=e.pendingLanes,c===0&&(wn=null),gf(n.stateNode),pt(e,Le()),t!==null)for(s=e.onRecoverableError,n=0;n<t.length;n++)l=t[n],s(l.value,{componentStack:l.stack,digest:l.digest});if(wo)throw wo=!1,e=Oa,Oa=null,e;return(So&1)!==0&&e.tag!==0&&Er(),c=e.pendingLanes,(c&1)!==0?e===Pa?hi++:(hi=0,Pa=e):hi=0,vn(),null}function Er(){if(kn!==null){var e=Gl(So),t=_t.transition,n=ge;try{if(_t.transition=null,ge=16>e?16:e,kn===null)var s=!1;else{if(e=kn,kn=null,So=0,(fe&6)!==0)throw Error(o(331));var l=fe;for(fe|=4,$=e.current;$!==null;){var c=$,p=c.child;if(($.flags&16)!==0){var m=c.deletions;if(m!==null){for(var h=0;h<m.length;h++){var S=m[h];for($=S;$!==null;){var T=$;switch(T.tag){case 0:case 11:case 15:di(8,T,c)}var O=T.child;if(O!==null)O.return=T,$=O;else for(;$!==null;){T=$;var R=T.sibling,M=T.return;if(Ju(T),T===S){$=null;break}if(R!==null){R.return=M,$=R;break}$=M}}}var H=c.alternate;if(H!==null){var W=H.child;if(W!==null){H.child=null;do{var Fe=W.sibling;W.sibling=null,W=Fe}while(W!==null)}}$=c}}if((c.subtreeFlags&2064)!==0&&p!==null)p.return=c,$=p;else e:for(;$!==null;){if(c=$,(c.flags&2048)!==0)switch(c.tag){case 0:case 11:case 15:di(9,c,c.return)}var w=c.sibling;if(w!==null){w.return=c.return,$=w;break e}$=c.return}}var y=e.current;for($=y;$!==null;){p=$;var k=p.child;if((p.subtreeFlags&2064)!==0&&k!==null)k.return=p,$=k;else e:for(p=y;$!==null;){if(m=$,(m.flags&2048)!==0)try{switch(m.tag){case 0:case 11:case 15:go(9,m)}}catch(K){De(m,m.return,K)}if(m===p){$=null;break e}var A=m.sibling;if(A!==null){A.return=m.return,$=A;break e}$=m.return}}if(fe=l,vn(),It&&typeof It.onPostCommitFiberRoot=="function")try{It.onPostCommitFiberRoot(Ri,e)}catch{}s=!0}return s}finally{ge=n,_t.transition=t}}return!1}function pp(e,t,n){t=xr(n,t),t=Ru(e,t,1),e=yn(e,t,1),t=st(),e!==null&&(Br(e,1,t),pt(e,t))}function De(e,t,n){if(e.tag===3)pp(e,e,n);else for(;t!==null;){if(t.tag===3){pp(t,e,n);break}else if(t.tag===1){var s=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof s.componentDidCatch=="function"&&(wn===null||!wn.has(s))){e=xr(n,e),e=Tu(t,e,1),t=yn(t,e,1),e=st(),t!==null&&(Br(t,1,e),pt(t,e));break}}t=t.return}}function Hm(e,t,n){var s=e.pingCache;s!==null&&s.delete(t),t=st(),e.pingedLanes|=e.suspendedLanes&n,Ke===e&&(Xe&n)===n&&($e===4||$e===3&&(Xe&130023424)===Xe&&500>Le()-Ta?qn(e,0):Ra|=n),pt(e,t)}function dp(e,t){t===0&&((e.mode&1)===0?t=1:(t=Oi,Oi<<=1,(Oi&130023424)===0&&(Oi=4194304)));var n=st();e=Zt(e,t),e!==null&&(Br(e,t,n),pt(e,n))}function Vm(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),dp(e,n)}function Wm(e,t){var n=0;switch(e.tag){case 13:var s=e.stateNode,l=e.memoizedState;l!==null&&(n=l.retryLane);break;case 19:s=e.stateNode;break;default:throw Error(o(314))}s!==null&&s.delete(t),dp(e,n)}var fp;fp=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||at.current)ct=!0;else{if((e.lanes&n)===0&&(t.flags&128)===0)return ct=!1,Am(e,t,n);ct=(e.flags&131072)!==0}else ct=!1,Ne&&(t.flags&1048576)!==0&&Wc(t,Zi,t.index);switch(t.lanes=0,t.tag){case 2:var s=t.type;ho(e,t),e=t.pendingProps;var l=pr(t,Ze.current);gr(t,n),l=aa(null,t,s,e,l,n);var c=la();return t.flags|=1,typeof l=="object"&&l!==null&&typeof l.render=="function"&&l.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,lt(s)?(c=!0,Yi(t)):c=!1,t.memoizedState=l.state!==null&&l.state!==void 0?l.state:null,ea(t),l.updater=fo,t.stateNode=l,l._reactInternals=t,ma(t,s,e,n),t=ya(null,t,s,!0,c,n)):(t.tag=0,Ne&&c&&Hs(t),ot(null,t,l,n),t=t.child),t;case 16:s=t.elementType;e:{switch(ho(e,t),e=t.pendingProps,l=s._init,s=l(s._payload),t.type=s,l=t.tag=Qm(s),e=Tt(s,e),l){case 0:t=ga(null,t,s,e,n);break e;case 1:t=Bu(null,t,s,e,n);break e;case 11:t=Du(null,t,s,e,n);break e;case 14:t=zu(null,t,s,Tt(s.type,e),n);break e}throw Error(o(306,s,""))}return t;case 0:return s=t.type,l=t.pendingProps,l=t.elementType===s?l:Tt(s,l),ga(e,t,s,l,n);case 1:return s=t.type,l=t.pendingProps,l=t.elementType===s?l:Tt(s,l),Bu(e,t,s,l,n);case 3:e:{if(Mu(t),e===null)throw Error(o(387));s=t.pendingProps,c=t.memoizedState,l=c.element,tu(e,t),oo(t,s,null,n);var p=t.memoizedState;if(s=p.element,c.isDehydrated)if(c={element:s,isDehydrated:!1,cache:p.cache,pendingSuspenseBoundaries:p.pendingSuspenseBoundaries,transitions:p.transitions},t.updateQueue.baseState=c,t.memoizedState=c,t.flags&256){l=xr(Error(o(423)),t),t=Uu(e,t,s,n,l);break e}else if(s!==l){l=xr(Error(o(424)),t),t=Uu(e,t,s,n,l);break e}else for(gt=fn(t.stateNode.containerInfo.firstChild),vt=t,Ne=!0,Rt=null,n=Zc(t,null,s,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(mr(),s===l){t=tn(e,t,n);break e}ot(e,t,s,n)}t=t.child}return t;case 5:return iu(t),e===null&&Ks(t),s=t.type,l=t.pendingProps,c=e!==null?e.memoizedProps:null,p=l.children,Is(s,l)?p=null:c!==null&&Is(s,c)&&(t.flags|=32),Iu(e,t),ot(e,t,p,n),t.child;case 6:return e===null&&Ks(t),null;case 13:return qu(e,t,n);case 4:return ta(t,t.stateNode.containerInfo),s=t.pendingProps,e===null?t.child=hr(t,null,s,n):ot(e,t,s,n),t.child;case 11:return s=t.type,l=t.pendingProps,l=t.elementType===s?l:Tt(s,l),Du(e,t,s,l,n);case 7:return ot(e,t,t.pendingProps,n),t.child;case 8:return ot(e,t,t.pendingProps.children,n),t.child;case 12:return ot(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(s=t.type._context,l=t.pendingProps,c=t.memoizedProps,p=l.value,Se(no,s._currentValue),s._currentValue=p,c!==null)if(jt(c.value,p)){if(c.children===l.children&&!at.current){t=tn(e,t,n);break e}}else for(c=t.child,c!==null&&(c.return=t);c!==null;){var m=c.dependencies;if(m!==null){p=c.child;for(var h=m.firstContext;h!==null;){if(h.context===s){if(c.tag===1){h=en(-1,n&-n),h.tag=2;var S=c.updateQueue;if(S!==null){S=S.shared;var T=S.pending;T===null?h.next=h:(h.next=T.next,T.next=h),S.pending=h}}c.lanes|=n,h=c.alternate,h!==null&&(h.lanes|=n),Gs(c.return,n,t),m.lanes|=n;break}h=h.next}}else if(c.tag===10)p=c.type===t.type?null:c.child;else if(c.tag===18){if(p=c.return,p===null)throw Error(o(341));p.lanes|=n,m=p.alternate,m!==null&&(m.lanes|=n),Gs(p,n,t),p=c.sibling}else p=c.child;if(p!==null)p.return=c;else for(p=c;p!==null;){if(p===t){p=null;break}if(c=p.sibling,c!==null){c.return=p.return,p=c;break}p=p.return}c=p}ot(e,t,l.children,n),t=t.child}return t;case 9:return l=t.type,s=t.pendingProps.children,gr(t,n),l=St(l),s=s(l),t.flags|=1,ot(e,t,s,n),t.child;case 14:return s=t.type,l=Tt(s,t.pendingProps),l=Tt(s.type,l),zu(e,t,s,l,n);case 15:return Lu(e,t,t.type,t.pendingProps,n);case 17:return s=t.type,l=t.pendingProps,l=t.elementType===s?l:Tt(s,l),ho(e,t),t.tag=1,lt(s)?(e=!0,Yi(t)):e=!1,gr(t,n),Nu(t,s,l),ma(t,s,l,n),ya(null,t,s,!0,e,n);case 19:return Hu(e,t,n);case 22:return Fu(e,t,n)}throw Error(o(156,t.tag))};function mp(e,t){return Kl(e,t)}function Km(e,t,n,s){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=s,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function bt(e,t,n,s){return new Km(e,t,n,s)}function Ia(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Qm(e){if(typeof e=="function")return Ia(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Ie)return 11;if(e===je)return 14}return 2}function _n(e,t){var n=e.alternate;return n===null?(n=bt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Co(e,t,n,s,l,c){var p=2;if(s=e,typeof e=="function")Ia(e)&&(p=1);else if(typeof e=="string")p=5;else e:switch(e){case ue:return Hn(n.children,l,c,t);case ke:p=8,l|=8;break;case ze:return e=bt(12,n,t,l|2),e.elementType=ze,e.lanes=c,e;case Be:return e=bt(13,n,t,l),e.elementType=Be,e.lanes=c,e;case ye:return e=bt(19,n,t,l),e.elementType=ye,e.lanes=c,e;case _e:return No(n,l,c,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Je:p=10;break e;case ae:p=9;break e;case Ie:p=11;break e;case je:p=14;break e;case Ge:p=16,s=null;break e}throw Error(o(130,e==null?e:typeof e,""))}return t=bt(p,n,t,l),t.elementType=e,t.type=s,t.lanes=c,t}function Hn(e,t,n,s){return e=bt(7,e,s,t),e.lanes=n,e}function No(e,t,n,s){return e=bt(22,e,s,t),e.elementType=_e,e.lanes=n,e.stateNode={isHidden:!1},e}function Ba(e,t,n){return e=bt(6,e,null,t),e.lanes=n,e}function Ma(e,t,n){return t=bt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Jm(e,t,n,s,l){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=fs(0),this.expirationTimes=fs(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=fs(0),this.identifierPrefix=s,this.onRecoverableError=l,this.mutableSourceEagerHydrationData=null}function Ua(e,t,n,s,l,c,p,m,h){return e=new Jm(e,t,n,m,h),t===1?(t=1,c===!0&&(t|=8)):t=0,c=bt(3,null,null,t),e.current=c,c.stateNode=e,c.memoizedState={element:s,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},ea(c),e}function Ym(e,t,n){var s=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:ce,key:s==null?null:""+s,children:e,containerInfo:t,implementation:n}}function hp(e){if(!e)return hn;e=e._reactInternals;e:{if(Pn(e)!==e||e.tag!==1)throw Error(o(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(lt(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(o(171))}if(e.tag===1){var n=e.type;if(lt(n))return $c(e,n,t)}return t}function vp(e,t,n,s,l,c,p,m,h){return e=Ua(n,s,!0,e,l,c,p,m,h),e.context=hp(null),n=e.current,s=st(),l=Sn(n),c=en(s,l),c.callback=t??null,yn(n,c,l),e.current.lanes=l,Br(e,l,s),pt(e,s),e}function jo(e,t,n,s){var l=t.current,c=st(),p=Sn(l);return n=hp(n),t.context===null?t.context=n:t.pendingContext=n,t=en(c,p),t.payload={element:e},s=s===void 0?null:s,s!==null&&(t.callback=s),e=yn(l,t,p),e!==null&&(At(e,l,p,c),io(e,l,p)),p}function Ro(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function gp(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function qa(e,t){gp(e,t),(e=e.alternate)&&gp(e,t)}function Xm(){return null}var yp=typeof reportError=="function"?reportError:function(e){console.error(e)};function $a(e){this._internalRoot=e}To.prototype.render=$a.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(o(409));jo(e,t,null,null)},To.prototype.unmount=$a.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Un(function(){jo(null,e,null,null)}),t[Jt]=null}};function To(e){this._internalRoot=e}To.prototype.unstable_scheduleHydration=function(e){if(e){var t=tc();e={blockedOn:null,target:e,priority:t};for(var n=0;n<un.length&&t!==0&&t<un[n].priority;n++);un.splice(n,0,e),n===0&&ic(e)}};function Ha(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Oo(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function xp(){}function Gm(e,t,n,s,l){if(l){if(typeof s=="function"){var c=s;s=function(){var S=Ro(p);c.call(S)}}var p=vp(t,s,e,0,null,!1,!1,"",xp);return e._reactRootContainer=p,e[Jt]=p.current,Zr(e.nodeType===8?e.parentNode:e),Un(),p}for(;l=e.lastChild;)e.removeChild(l);if(typeof s=="function"){var m=s;s=function(){var S=Ro(h);m.call(S)}}var h=Ua(e,0,!1,null,null,!1,!1,"",xp);return e._reactRootContainer=h,e[Jt]=h.current,Zr(e.nodeType===8?e.parentNode:e),Un(function(){jo(t,h,n,s)}),h}function Po(e,t,n,s,l){var c=n._reactRootContainer;if(c){var p=c;if(typeof l=="function"){var m=l;l=function(){var h=Ro(p);m.call(h)}}jo(t,p,e,l)}else p=Gm(n,t,e,l,s);return Ro(p)}Zl=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Ir(t.pendingLanes);n!==0&&(ms(t,n|1),pt(t,Le()),(fe&6)===0&&(Sr=Le()+500,vn()))}break;case 13:Un(function(){var s=Zt(e,1);if(s!==null){var l=st();At(s,e,1,l)}}),qa(e,1)}},hs=function(e){if(e.tag===13){var t=Zt(e,134217728);if(t!==null){var n=st();At(t,e,134217728,n)}qa(e,134217728)}},ec=function(e){if(e.tag===13){var t=Sn(e),n=Zt(e,t);if(n!==null){var s=st();At(n,e,t,s)}qa(e,t)}},tc=function(){return ge},nc=function(e,t){var n=ge;try{return ge=e,t()}finally{ge=n}},as=function(e,t,n){switch(t){case"input":if(Lt(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var s=n[t];if(s!==e&&s.form===e.form){var l=Qi(s);if(!l)throw Error(o(90));Kt(s),Lt(s,l)}}}break;case"textarea":I(e,n);break;case"select":t=n.value,t!=null&&on(e,!!n.multiple,t,!1)}},Ml=za,Ul=Un;var Zm={usingClientEntryPoint:!1,Events:[ni,cr,Qi,Il,Bl,za]},vi={findFiberByHostInstance:An,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},eh={bundleType:vi.bundleType,version:vi.version,rendererPackageName:vi.rendererPackageName,rendererConfig:vi.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:U.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Vl(e),e===null?null:e.stateNode},findFiberByHostInstance:vi.findFiberByHostInstance||Xm,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ao=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ao.isDisabled&&Ao.supportsFiber)try{Ri=Ao.inject(eh),It=Ao}catch{}}return dt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Zm,dt.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Ha(t))throw Error(o(200));return Ym(e,t,null,n)},dt.createRoot=function(e,t){if(!Ha(e))throw Error(o(299));var n=!1,s="",l=yp;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(s=t.identifierPrefix),t.onRecoverableError!==void 0&&(l=t.onRecoverableError)),t=Ua(e,1,!1,null,null,n,!1,s,l),e[Jt]=t.current,Zr(e.nodeType===8?e.parentNode:e),new $a(t)},dt.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(o(188)):(e=Object.keys(e).join(","),Error(o(268,e)));return e=Vl(t),e=e===null?null:e.stateNode,e},dt.flushSync=function(e){return Un(e)},dt.hydrate=function(e,t,n){if(!Oo(t))throw Error(o(200));return Po(null,e,t,!0,n)},dt.hydrateRoot=function(e,t,n){if(!Ha(e))throw Error(o(405));var s=n!=null&&n.hydratedSources||null,l=!1,c="",p=yp;if(n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(c=n.identifierPrefix),n.onRecoverableError!==void 0&&(p=n.onRecoverableError)),t=vp(t,null,e,1,n??null,l,!1,c,p),e[Jt]=t.current,Zr(e),s)for(e=0;e<s.length;e++)n=s[e],l=n._getVersion,l=l(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,l]:t.mutableSourceEagerHydrationData.push(n,l);return new To(t)},dt.render=function(e,t,n){if(!Oo(t))throw Error(o(200));return Po(null,e,t,!1,n)},dt.unmountComponentAtNode=function(e){if(!Oo(e))throw Error(o(40));return e._reactRootContainer?(Un(function(){Po(null,null,e,!1,function(){e._reactRootContainer=null,e[Jt]=null})}),!0):!1},dt.unstable_batchedUpdates=za,dt.unstable_renderSubtreeIntoContainer=function(e,t,n,s){if(!Oo(n))throw Error(o(200));if(e==null||e._reactInternals===void 0)throw Error(o(38));return Po(e,t,n,!1,s)},dt.version="18.3.1-next-f1338f8080-20240426",dt}var Np;function ch(){if(Np)return Ka.exports;Np=1;function i(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(i)}catch(r){console.error(r)}}return i(),Ka.exports=lh(),Ka.exports}var jp;function uh(){if(jp)return Do;jp=1;var i=ch();return Do.createRoot=i.createRoot,Do.hydrateRoot=i.hydrateRoot,Do}var ph=uh();function pd(i,r){return function(){return i.apply(r,arguments)}}const{toString:dh}=Object.prototype,{getPrototypeOf:jl}=Object,{iterator:Go,toStringTag:dd}=Symbol,Zo=(i=>r=>{const o=dh.call(r);return i[o]||(i[o]=o.slice(8,-1).toLowerCase())})(Object.create(null)),zt=i=>(i=i.toLowerCase(),r=>Zo(r)===i),es=i=>r=>typeof r===i,{isArray:Cr}=Array,ki=es("undefined");function fh(i){return i!==null&&!ki(i)&&i.constructor!==null&&!ki(i.constructor)&&ft(i.constructor.isBuffer)&&i.constructor.isBuffer(i)}const fd=zt("ArrayBuffer");function mh(i){let r;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?r=ArrayBuffer.isView(i):r=i&&i.buffer&&fd(i.buffer),r}const hh=es("string"),ft=es("function"),md=es("number"),ts=i=>i!==null&&typeof i=="object",vh=i=>i===!0||i===!1,Bo=i=>{if(Zo(i)!=="object")return!1;const r=jl(i);return(r===null||r===Object.prototype||Object.getPrototypeOf(r)===null)&&!(dd in i)&&!(Go in i)},gh=zt("Date"),yh=zt("File"),xh=zt("Blob"),wh=zt("FileList"),kh=i=>ts(i)&&ft(i.pipe),Sh=i=>{let r;return i&&(typeof FormData=="function"&&i instanceof FormData||ft(i.append)&&((r=Zo(i))==="formdata"||r==="object"&&ft(i.toString)&&i.toString()==="[object FormData]"))},Eh=zt("URLSearchParams"),[_h,bh,Ch,Nh]=["ReadableStream","Request","Response","Headers"].map(zt),jh=i=>i.trim?i.trim():i.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Ei(i,r,{allOwnKeys:o=!1}={}){if(i===null||typeof i>"u")return;let a,u;if(typeof i!="object"&&(i=[i]),Cr(i))for(a=0,u=i.length;a<u;a++)r.call(null,i[a],a,i);else{const d=o?Object.getOwnPropertyNames(i):Object.keys(i),f=d.length;let g;for(a=0;a<f;a++)g=d[a],r.call(null,i[g],g,i)}}function hd(i,r){r=r.toLowerCase();const o=Object.keys(i);let a=o.length,u;for(;a-- >0;)if(u=o[a],r===u.toLowerCase())return u;return null}const Kn=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,vd=i=>!ki(i)&&i!==Kn;function ll(){const{caseless:i}=vd(this)&&this||{},r={},o=(a,u)=>{const d=i&&hd(r,u)||u;Bo(r[d])&&Bo(a)?r[d]=ll(r[d],a):Bo(a)?r[d]=ll({},a):Cr(a)?r[d]=a.slice():r[d]=a};for(let a=0,u=arguments.length;a<u;a++)arguments[a]&&Ei(arguments[a],o);return r}const Rh=(i,r,o,{allOwnKeys:a}={})=>(Ei(r,(u,d)=>{o&&ft(u)?i[d]=pd(u,o):i[d]=u},{allOwnKeys:a}),i),Th=i=>(i.charCodeAt(0)===65279&&(i=i.slice(1)),i),Oh=(i,r,o,a)=>{i.prototype=Object.create(r.prototype,a),i.prototype.constructor=i,Object.defineProperty(i,"super",{value:r.prototype}),o&&Object.assign(i.prototype,o)},Ph=(i,r,o,a)=>{let u,d,f;const g={};if(r=r||{},i==null)return r;do{for(u=Object.getOwnPropertyNames(i),d=u.length;d-- >0;)f=u[d],(!a||a(f,i,r))&&!g[f]&&(r[f]=i[f],g[f]=!0);i=o!==!1&&jl(i)}while(i&&(!o||o(i,r))&&i!==Object.prototype);return r},Ah=(i,r,o)=>{i=String(i),(o===void 0||o>i.length)&&(o=i.length),o-=r.length;const a=i.indexOf(r,o);return a!==-1&&a===o},Dh=i=>{if(!i)return null;if(Cr(i))return i;let r=i.length;if(!md(r))return null;const o=new Array(r);for(;r-- >0;)o[r]=i[r];return o},zh=(i=>r=>i&&r instanceof i)(typeof Uint8Array<"u"&&jl(Uint8Array)),Lh=(i,r)=>{const a=(i&&i[Go]).call(i);let u;for(;(u=a.next())&&!u.done;){const d=u.value;r.call(i,d[0],d[1])}},Fh=(i,r)=>{let o;const a=[];for(;(o=i.exec(r))!==null;)a.push(o);return a},Ih=zt("HTMLFormElement"),Bh=i=>i.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(o,a,u){return a.toUpperCase()+u}),Rp=(({hasOwnProperty:i})=>(r,o)=>i.call(r,o))(Object.prototype),Mh=zt("RegExp"),gd=(i,r)=>{const o=Object.getOwnPropertyDescriptors(i),a={};Ei(o,(u,d)=>{let f;(f=r(u,d,i))!==!1&&(a[d]=f||u)}),Object.defineProperties(i,a)},Uh=i=>{gd(i,(r,o)=>{if(ft(i)&&["arguments","caller","callee"].indexOf(o)!==-1)return!1;const a=i[o];if(ft(a)){if(r.enumerable=!1,"writable"in r){r.writable=!1;return}r.set||(r.set=()=>{throw Error("Can not rewrite read-only method '"+o+"'")})}})},qh=(i,r)=>{const o={},a=u=>{u.forEach(d=>{o[d]=!0})};return Cr(i)?a(i):a(String(i).split(r)),o},$h=()=>{},Hh=(i,r)=>i!=null&&Number.isFinite(i=+i)?i:r;function Vh(i){return!!(i&&ft(i.append)&&i[dd]==="FormData"&&i[Go])}const Wh=i=>{const r=new Array(10),o=(a,u)=>{if(ts(a)){if(r.indexOf(a)>=0)return;if(!("toJSON"in a)){r[u]=a;const d=Cr(a)?[]:{};return Ei(a,(f,g)=>{const E=o(f,u+1);!ki(E)&&(d[g]=E)}),r[u]=void 0,d}}return a};return o(i,0)},Kh=zt("AsyncFunction"),Qh=i=>i&&(ts(i)||ft(i))&&ft(i.then)&&ft(i.catch),yd=((i,r)=>i?setImmediate:r?((o,a)=>(Kn.addEventListener("message",({source:u,data:d})=>{u===Kn&&d===o&&a.length&&a.shift()()},!1),u=>{a.push(u),Kn.postMessage(o,"*")}))(`axios@${Math.random()}`,[]):o=>setTimeout(o))(typeof setImmediate=="function",ft(Kn.postMessage)),Jh=typeof queueMicrotask<"u"?queueMicrotask.bind(Kn):typeof process<"u"&&process.nextTick||yd,Yh=i=>i!=null&&ft(i[Go]),C={isArray:Cr,isArrayBuffer:fd,isBuffer:fh,isFormData:Sh,isArrayBufferView:mh,isString:hh,isNumber:md,isBoolean:vh,isObject:ts,isPlainObject:Bo,isReadableStream:_h,isRequest:bh,isResponse:Ch,isHeaders:Nh,isUndefined:ki,isDate:gh,isFile:yh,isBlob:xh,isRegExp:Mh,isFunction:ft,isStream:kh,isURLSearchParams:Eh,isTypedArray:zh,isFileList:wh,forEach:Ei,merge:ll,extend:Rh,trim:jh,stripBOM:Th,inherits:Oh,toFlatObject:Ph,kindOf:Zo,kindOfTest:zt,endsWith:Ah,toArray:Dh,forEachEntry:Lh,matchAll:Fh,isHTMLForm:Ih,hasOwnProperty:Rp,hasOwnProp:Rp,reduceDescriptors:gd,freezeMethods:Uh,toObjectSet:qh,toCamelCase:Bh,noop:$h,toFiniteNumber:Hh,findKey:hd,global:Kn,isContextDefined:vd,isSpecCompliantForm:Vh,toJSONObject:Wh,isAsyncFn:Kh,isThenable:Qh,setImmediate:yd,asap:Jh,isIterable:Yh};function ie(i,r,o,a,u){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=i,this.name="AxiosError",r&&(this.code=r),o&&(this.config=o),a&&(this.request=a),u&&(this.response=u,this.status=u.status?u.status:null)}C.inherits(ie,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:C.toJSONObject(this.config),code:this.code,status:this.status}}});const xd=ie.prototype,wd={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(i=>{wd[i]={value:i}});Object.defineProperties(ie,wd);Object.defineProperty(xd,"isAxiosError",{value:!0});ie.from=(i,r,o,a,u,d)=>{const f=Object.create(xd);return C.toFlatObject(i,f,function(E){return E!==Error.prototype},g=>g!=="isAxiosError"),ie.call(f,i.message,r,o,a,u),f.cause=i,f.name=i.name,d&&Object.assign(f,d),f};const Xh=null;function cl(i){return C.isPlainObject(i)||C.isArray(i)}function kd(i){return C.endsWith(i,"[]")?i.slice(0,-2):i}function Tp(i,r,o){return i?i.concat(r).map(function(u,d){return u=kd(u),!o&&d?"["+u+"]":u}).join(o?".":""):r}function Gh(i){return C.isArray(i)&&!i.some(cl)}const Zh=C.toFlatObject(C,{},null,function(r){return/^is[A-Z]/.test(r)});function ns(i,r,o){if(!C.isObject(i))throw new TypeError("target must be an object");r=r||new FormData,o=C.toFlatObject(o,{metaTokens:!0,dots:!1,indexes:!1},!1,function(L,P){return!C.isUndefined(P[L])});const a=o.metaTokens,u=o.visitor||b,d=o.dots,f=o.indexes,E=(o.Blob||typeof Blob<"u"&&Blob)&&C.isSpecCompliantForm(r);if(!C.isFunction(u))throw new TypeError("visitor must be a function");function _(D){if(D===null)return"";if(C.isDate(D))return D.toISOString();if(C.isBoolean(D))return D.toString();if(!E&&C.isBlob(D))throw new ie("Blob is not supported. Use a Buffer instead.");return C.isArrayBuffer(D)||C.isTypedArray(D)?E&&typeof Blob=="function"?new Blob([D]):Buffer.from(D):D}function b(D,L,P){let se=D;if(D&&!P&&typeof D=="object"){if(C.endsWith(L,"{}"))L=a?L:L.slice(0,-2),D=JSON.stringify(D);else if(C.isArray(D)&&Gh(D)||(C.isFileList(D)||C.endsWith(L,"[]"))&&(se=C.toArray(D)))return L=kd(L),se.forEach(function(B,U){!(C.isUndefined(B)||B===null)&&r.append(f===!0?Tp([L],U,d):f===null?L:L+"[]",_(B))}),!1}return cl(D)?!0:(r.append(Tp(P,L,d),_(D)),!1)}const N=[],z=Object.assign(Zh,{defaultVisitor:b,convertValue:_,isVisitable:cl});function J(D,L){if(!C.isUndefined(D)){if(N.indexOf(D)!==-1)throw Error("Circular reference detected in "+L.join("."));N.push(D),C.forEach(D,function(se,Q){(!(C.isUndefined(se)||se===null)&&u.call(r,se,C.isString(Q)?Q.trim():Q,L,z))===!0&&J(se,L?L.concat(Q):[Q])}),N.pop()}}if(!C.isObject(i))throw new TypeError("data must be an object");return J(i),r}function Op(i){const r={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(i).replace(/[!'()~]|%20|%00/g,function(a){return r[a]})}function Rl(i,r){this._pairs=[],i&&ns(i,this,r)}const Sd=Rl.prototype;Sd.append=function(r,o){this._pairs.push([r,o])};Sd.toString=function(r){const o=r?function(a){return r.call(this,a,Op)}:Op;return this._pairs.map(function(u){return o(u[0])+"="+o(u[1])},"").join("&")};function ev(i){return encodeURIComponent(i).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Ed(i,r,o){if(!r)return i;const a=o&&o.encode||ev;C.isFunction(o)&&(o={serialize:o});const u=o&&o.serialize;let d;if(u?d=u(r,o):d=C.isURLSearchParams(r)?r.toString():new Rl(r,o).toString(a),d){const f=i.indexOf("#");f!==-1&&(i=i.slice(0,f)),i+=(i.indexOf("?")===-1?"?":"&")+d}return i}class Pp{constructor(){this.handlers=[]}use(r,o,a){return this.handlers.push({fulfilled:r,rejected:o,synchronous:a?a.synchronous:!1,runWhen:a?a.runWhen:null}),this.handlers.length-1}eject(r){this.handlers[r]&&(this.handlers[r]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(r){C.forEach(this.handlers,function(a){a!==null&&r(a)})}}const _d={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},tv=typeof URLSearchParams<"u"?URLSearchParams:Rl,nv=typeof FormData<"u"?FormData:null,rv=typeof Blob<"u"?Blob:null,iv={isBrowser:!0,classes:{URLSearchParams:tv,FormData:nv,Blob:rv},protocols:["http","https","file","blob","url","data"]},Tl=typeof window<"u"&&typeof document<"u",ul=typeof navigator=="object"&&navigator||void 0,ov=Tl&&(!ul||["ReactNative","NativeScript","NS"].indexOf(ul.product)<0),sv=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",av=Tl&&window.location.href||"http://localhost",lv=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Tl,hasStandardBrowserEnv:ov,hasStandardBrowserWebWorkerEnv:sv,navigator:ul,origin:av},Symbol.toStringTag,{value:"Module"})),rt={...lv,...iv};function cv(i,r){return ns(i,new rt.classes.URLSearchParams,Object.assign({visitor:function(o,a,u,d){return rt.isNode&&C.isBuffer(o)?(this.append(a,o.toString("base64")),!1):d.defaultVisitor.apply(this,arguments)}},r))}function uv(i){return C.matchAll(/\w+|\[(\w*)]/g,i).map(r=>r[0]==="[]"?"":r[1]||r[0])}function pv(i){const r={},o=Object.keys(i);let a;const u=o.length;let d;for(a=0;a<u;a++)d=o[a],r[d]=i[d];return r}function bd(i){function r(o,a,u,d){let f=o[d++];if(f==="__proto__")return!0;const g=Number.isFinite(+f),E=d>=o.length;return f=!f&&C.isArray(u)?u.length:f,E?(C.hasOwnProp(u,f)?u[f]=[u[f],a]:u[f]=a,!g):((!u[f]||!C.isObject(u[f]))&&(u[f]=[]),r(o,a,u[f],d)&&C.isArray(u[f])&&(u[f]=pv(u[f])),!g)}if(C.isFormData(i)&&C.isFunction(i.entries)){const o={};return C.forEachEntry(i,(a,u)=>{r(uv(a),u,o,0)}),o}return null}function dv(i,r,o){if(C.isString(i))try{return(r||JSON.parse)(i),C.trim(i)}catch(a){if(a.name!=="SyntaxError")throw a}return(o||JSON.stringify)(i)}const _i={transitional:_d,adapter:["xhr","http","fetch"],transformRequest:[function(r,o){const a=o.getContentType()||"",u=a.indexOf("application/json")>-1,d=C.isObject(r);if(d&&C.isHTMLForm(r)&&(r=new FormData(r)),C.isFormData(r))return u?JSON.stringify(bd(r)):r;if(C.isArrayBuffer(r)||C.isBuffer(r)||C.isStream(r)||C.isFile(r)||C.isBlob(r)||C.isReadableStream(r))return r;if(C.isArrayBufferView(r))return r.buffer;if(C.isURLSearchParams(r))return o.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),r.toString();let g;if(d){if(a.indexOf("application/x-www-form-urlencoded")>-1)return cv(r,this.formSerializer).toString();if((g=C.isFileList(r))||a.indexOf("multipart/form-data")>-1){const E=this.env&&this.env.FormData;return ns(g?{"files[]":r}:r,E&&new E,this.formSerializer)}}return d||u?(o.setContentType("application/json",!1),dv(r)):r}],transformResponse:[function(r){const o=this.transitional||_i.transitional,a=o&&o.forcedJSONParsing,u=this.responseType==="json";if(C.isResponse(r)||C.isReadableStream(r))return r;if(r&&C.isString(r)&&(a&&!this.responseType||u)){const f=!(o&&o.silentJSONParsing)&&u;try{return JSON.parse(r)}catch(g){if(f)throw g.name==="SyntaxError"?ie.from(g,ie.ERR_BAD_RESPONSE,this,null,this.response):g}}return r}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:rt.classes.FormData,Blob:rt.classes.Blob},validateStatus:function(r){return r>=200&&r<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};C.forEach(["delete","get","head","post","put","patch"],i=>{_i.headers[i]={}});const fv=C.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),mv=i=>{const r={};let o,a,u;return i&&i.split(`
`).forEach(function(f){u=f.indexOf(":"),o=f.substring(0,u).trim().toLowerCase(),a=f.substring(u+1).trim(),!(!o||r[o]&&fv[o])&&(o==="set-cookie"?r[o]?r[o].push(a):r[o]=[a]:r[o]=r[o]?r[o]+", "+a:a)}),r},Ap=Symbol("internals");function yi(i){return i&&String(i).trim().toLowerCase()}function Mo(i){return i===!1||i==null?i:C.isArray(i)?i.map(Mo):String(i)}function hv(i){const r=Object.create(null),o=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let a;for(;a=o.exec(i);)r[a[1]]=a[2];return r}const vv=i=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(i.trim());function Ya(i,r,o,a,u){if(C.isFunction(a))return a.call(this,r,o);if(u&&(r=o),!!C.isString(r)){if(C.isString(a))return r.indexOf(a)!==-1;if(C.isRegExp(a))return a.test(r)}}function gv(i){return i.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(r,o,a)=>o.toUpperCase()+a)}function yv(i,r){const o=C.toCamelCase(" "+r);["get","set","has"].forEach(a=>{Object.defineProperty(i,a+o,{value:function(u,d,f){return this[a].call(this,r,u,d,f)},configurable:!0})})}let mt=class{constructor(r){r&&this.set(r)}set(r,o,a){const u=this;function d(g,E,_){const b=yi(E);if(!b)throw new Error("header name must be a non-empty string");const N=C.findKey(u,b);(!N||u[N]===void 0||_===!0||_===void 0&&u[N]!==!1)&&(u[N||E]=Mo(g))}const f=(g,E)=>C.forEach(g,(_,b)=>d(_,b,E));if(C.isPlainObject(r)||r instanceof this.constructor)f(r,o);else if(C.isString(r)&&(r=r.trim())&&!vv(r))f(mv(r),o);else if(C.isObject(r)&&C.isIterable(r)){let g={},E,_;for(const b of r){if(!C.isArray(b))throw TypeError("Object iterator must return a key-value pair");g[_=b[0]]=(E=g[_])?C.isArray(E)?[...E,b[1]]:[E,b[1]]:b[1]}f(g,o)}else r!=null&&d(o,r,a);return this}get(r,o){if(r=yi(r),r){const a=C.findKey(this,r);if(a){const u=this[a];if(!o)return u;if(o===!0)return hv(u);if(C.isFunction(o))return o.call(this,u,a);if(C.isRegExp(o))return o.exec(u);throw new TypeError("parser must be boolean|regexp|function")}}}has(r,o){if(r=yi(r),r){const a=C.findKey(this,r);return!!(a&&this[a]!==void 0&&(!o||Ya(this,this[a],a,o)))}return!1}delete(r,o){const a=this;let u=!1;function d(f){if(f=yi(f),f){const g=C.findKey(a,f);g&&(!o||Ya(a,a[g],g,o))&&(delete a[g],u=!0)}}return C.isArray(r)?r.forEach(d):d(r),u}clear(r){const o=Object.keys(this);let a=o.length,u=!1;for(;a--;){const d=o[a];(!r||Ya(this,this[d],d,r,!0))&&(delete this[d],u=!0)}return u}normalize(r){const o=this,a={};return C.forEach(this,(u,d)=>{const f=C.findKey(a,d);if(f){o[f]=Mo(u),delete o[d];return}const g=r?gv(d):String(d).trim();g!==d&&delete o[d],o[g]=Mo(u),a[g]=!0}),this}concat(...r){return this.constructor.concat(this,...r)}toJSON(r){const o=Object.create(null);return C.forEach(this,(a,u)=>{a!=null&&a!==!1&&(o[u]=r&&C.isArray(a)?a.join(", "):a)}),o}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([r,o])=>r+": "+o).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(r){return r instanceof this?r:new this(r)}static concat(r,...o){const a=new this(r);return o.forEach(u=>a.set(u)),a}static accessor(r){const a=(this[Ap]=this[Ap]={accessors:{}}).accessors,u=this.prototype;function d(f){const g=yi(f);a[g]||(yv(u,f),a[g]=!0)}return C.isArray(r)?r.forEach(d):d(r),this}};mt.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);C.reduceDescriptors(mt.prototype,({value:i},r)=>{let o=r[0].toUpperCase()+r.slice(1);return{get:()=>i,set(a){this[o]=a}}});C.freezeMethods(mt);function Xa(i,r){const o=this||_i,a=r||o,u=mt.from(a.headers);let d=a.data;return C.forEach(i,function(g){d=g.call(o,d,u.normalize(),r?r.status:void 0)}),u.normalize(),d}function Cd(i){return!!(i&&i.__CANCEL__)}function Nr(i,r,o){ie.call(this,i??"canceled",ie.ERR_CANCELED,r,o),this.name="CanceledError"}C.inherits(Nr,ie,{__CANCEL__:!0});function Nd(i,r,o){const a=o.config.validateStatus;!o.status||!a||a(o.status)?i(o):r(new ie("Request failed with status code "+o.status,[ie.ERR_BAD_REQUEST,ie.ERR_BAD_RESPONSE][Math.floor(o.status/100)-4],o.config,o.request,o))}function xv(i){const r=/^([-+\w]{1,25})(:?\/\/|:)/.exec(i);return r&&r[1]||""}function wv(i,r){i=i||10;const o=new Array(i),a=new Array(i);let u=0,d=0,f;return r=r!==void 0?r:1e3,function(E){const _=Date.now(),b=a[d];f||(f=_),o[u]=E,a[u]=_;let N=d,z=0;for(;N!==u;)z+=o[N++],N=N%i;if(u=(u+1)%i,u===d&&(d=(d+1)%i),_-f<r)return;const J=b&&_-b;return J?Math.round(z*1e3/J):void 0}}function kv(i,r){let o=0,a=1e3/r,u,d;const f=(_,b=Date.now())=>{o=b,u=null,d&&(clearTimeout(d),d=null),i.apply(null,_)};return[(..._)=>{const b=Date.now(),N=b-o;N>=a?f(_,b):(u=_,d||(d=setTimeout(()=>{d=null,f(u)},a-N)))},()=>u&&f(u)]}const Ko=(i,r,o=3)=>{let a=0;const u=wv(50,250);return kv(d=>{const f=d.loaded,g=d.lengthComputable?d.total:void 0,E=f-a,_=u(E),b=f<=g;a=f;const N={loaded:f,total:g,progress:g?f/g:void 0,bytes:E,rate:_||void 0,estimated:_&&g&&b?(g-f)/_:void 0,event:d,lengthComputable:g!=null,[r?"download":"upload"]:!0};i(N)},o)},Dp=(i,r)=>{const o=i!=null;return[a=>r[0]({lengthComputable:o,total:i,loaded:a}),r[1]]},zp=i=>(...r)=>C.asap(()=>i(...r)),Sv=rt.hasStandardBrowserEnv?((i,r)=>o=>(o=new URL(o,rt.origin),i.protocol===o.protocol&&i.host===o.host&&(r||i.port===o.port)))(new URL(rt.origin),rt.navigator&&/(msie|trident)/i.test(rt.navigator.userAgent)):()=>!0,Ev=rt.hasStandardBrowserEnv?{write(i,r,o,a,u,d){const f=[i+"="+encodeURIComponent(r)];C.isNumber(o)&&f.push("expires="+new Date(o).toGMTString()),C.isString(a)&&f.push("path="+a),C.isString(u)&&f.push("domain="+u),d===!0&&f.push("secure"),document.cookie=f.join("; ")},read(i){const r=document.cookie.match(new RegExp("(^|;\\s*)("+i+")=([^;]*)"));return r?decodeURIComponent(r[3]):null},remove(i){this.write(i,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function _v(i){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(i)}function bv(i,r){return r?i.replace(/\/?\/$/,"")+"/"+r.replace(/^\/+/,""):i}function jd(i,r,o){let a=!_v(r);return i&&(a||o==!1)?bv(i,r):r}const Lp=i=>i instanceof mt?{...i}:i;function Jn(i,r){r=r||{};const o={};function a(_,b,N,z){return C.isPlainObject(_)&&C.isPlainObject(b)?C.merge.call({caseless:z},_,b):C.isPlainObject(b)?C.merge({},b):C.isArray(b)?b.slice():b}function u(_,b,N,z){if(C.isUndefined(b)){if(!C.isUndefined(_))return a(void 0,_,N,z)}else return a(_,b,N,z)}function d(_,b){if(!C.isUndefined(b))return a(void 0,b)}function f(_,b){if(C.isUndefined(b)){if(!C.isUndefined(_))return a(void 0,_)}else return a(void 0,b)}function g(_,b,N){if(N in r)return a(_,b);if(N in i)return a(void 0,_)}const E={url:d,method:d,data:d,baseURL:f,transformRequest:f,transformResponse:f,paramsSerializer:f,timeout:f,timeoutMessage:f,withCredentials:f,withXSRFToken:f,adapter:f,responseType:f,xsrfCookieName:f,xsrfHeaderName:f,onUploadProgress:f,onDownloadProgress:f,decompress:f,maxContentLength:f,maxBodyLength:f,beforeRedirect:f,transport:f,httpAgent:f,httpsAgent:f,cancelToken:f,socketPath:f,responseEncoding:f,validateStatus:g,headers:(_,b,N)=>u(Lp(_),Lp(b),N,!0)};return C.forEach(Object.keys(Object.assign({},i,r)),function(b){const N=E[b]||u,z=N(i[b],r[b],b);C.isUndefined(z)&&N!==g||(o[b]=z)}),o}const Rd=i=>{const r=Jn({},i);let{data:o,withXSRFToken:a,xsrfHeaderName:u,xsrfCookieName:d,headers:f,auth:g}=r;r.headers=f=mt.from(f),r.url=Ed(jd(r.baseURL,r.url,r.allowAbsoluteUrls),i.params,i.paramsSerializer),g&&f.set("Authorization","Basic "+btoa((g.username||"")+":"+(g.password?unescape(encodeURIComponent(g.password)):"")));let E;if(C.isFormData(o)){if(rt.hasStandardBrowserEnv||rt.hasStandardBrowserWebWorkerEnv)f.setContentType(void 0);else if((E=f.getContentType())!==!1){const[_,...b]=E?E.split(";").map(N=>N.trim()).filter(Boolean):[];f.setContentType([_||"multipart/form-data",...b].join("; "))}}if(rt.hasStandardBrowserEnv&&(a&&C.isFunction(a)&&(a=a(r)),a||a!==!1&&Sv(r.url))){const _=u&&d&&Ev.read(d);_&&f.set(u,_)}return r},Cv=typeof XMLHttpRequest<"u",Nv=Cv&&function(i){return new Promise(function(o,a){const u=Rd(i);let d=u.data;const f=mt.from(u.headers).normalize();let{responseType:g,onUploadProgress:E,onDownloadProgress:_}=u,b,N,z,J,D;function L(){J&&J(),D&&D(),u.cancelToken&&u.cancelToken.unsubscribe(b),u.signal&&u.signal.removeEventListener("abort",b)}let P=new XMLHttpRequest;P.open(u.method.toUpperCase(),u.url,!0),P.timeout=u.timeout;function se(){if(!P)return;const B=mt.from("getAllResponseHeaders"in P&&P.getAllResponseHeaders()),ne={data:!g||g==="text"||g==="json"?P.responseText:P.response,status:P.status,statusText:P.statusText,headers:B,config:i,request:P};Nd(function(ue){o(ue),L()},function(ue){a(ue),L()},ne),P=null}"onloadend"in P?P.onloadend=se:P.onreadystatechange=function(){!P||P.readyState!==4||P.status===0&&!(P.responseURL&&P.responseURL.indexOf("file:")===0)||setTimeout(se)},P.onabort=function(){P&&(a(new ie("Request aborted",ie.ECONNABORTED,i,P)),P=null)},P.onerror=function(){a(new ie("Network Error",ie.ERR_NETWORK,i,P)),P=null},P.ontimeout=function(){let U=u.timeout?"timeout of "+u.timeout+"ms exceeded":"timeout exceeded";const ne=u.transitional||_d;u.timeoutErrorMessage&&(U=u.timeoutErrorMessage),a(new ie(U,ne.clarifyTimeoutError?ie.ETIMEDOUT:ie.ECONNABORTED,i,P)),P=null},d===void 0&&f.setContentType(null),"setRequestHeader"in P&&C.forEach(f.toJSON(),function(U,ne){P.setRequestHeader(ne,U)}),C.isUndefined(u.withCredentials)||(P.withCredentials=!!u.withCredentials),g&&g!=="json"&&(P.responseType=u.responseType),_&&([z,D]=Ko(_,!0),P.addEventListener("progress",z)),E&&P.upload&&([N,J]=Ko(E),P.upload.addEventListener("progress",N),P.upload.addEventListener("loadend",J)),(u.cancelToken||u.signal)&&(b=B=>{P&&(a(!B||B.type?new Nr(null,i,P):B),P.abort(),P=null)},u.cancelToken&&u.cancelToken.subscribe(b),u.signal&&(u.signal.aborted?b():u.signal.addEventListener("abort",b)));const Q=xv(u.url);if(Q&&rt.protocols.indexOf(Q)===-1){a(new ie("Unsupported protocol "+Q+":",ie.ERR_BAD_REQUEST,i));return}P.send(d||null)})},jv=(i,r)=>{const{length:o}=i=i?i.filter(Boolean):[];if(r||o){let a=new AbortController,u;const d=function(_){if(!u){u=!0,g();const b=_ instanceof Error?_:this.reason;a.abort(b instanceof ie?b:new Nr(b instanceof Error?b.message:b))}};let f=r&&setTimeout(()=>{f=null,d(new ie(`timeout ${r} of ms exceeded`,ie.ETIMEDOUT))},r);const g=()=>{i&&(f&&clearTimeout(f),f=null,i.forEach(_=>{_.unsubscribe?_.unsubscribe(d):_.removeEventListener("abort",d)}),i=null)};i.forEach(_=>_.addEventListener("abort",d));const{signal:E}=a;return E.unsubscribe=()=>C.asap(g),E}},Rv=function*(i,r){let o=i.byteLength;if(o<r){yield i;return}let a=0,u;for(;a<o;)u=a+r,yield i.slice(a,u),a=u},Tv=async function*(i,r){for await(const o of Ov(i))yield*Rv(o,r)},Ov=async function*(i){if(i[Symbol.asyncIterator]){yield*i;return}const r=i.getReader();try{for(;;){const{done:o,value:a}=await r.read();if(o)break;yield a}}finally{await r.cancel()}},Fp=(i,r,o,a)=>{const u=Tv(i,r);let d=0,f,g=E=>{f||(f=!0,a&&a(E))};return new ReadableStream({async pull(E){try{const{done:_,value:b}=await u.next();if(_){g(),E.close();return}let N=b.byteLength;if(o){let z=d+=N;o(z)}E.enqueue(new Uint8Array(b))}catch(_){throw g(_),_}},cancel(E){return g(E),u.return()}},{highWaterMark:2})},rs=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Td=rs&&typeof ReadableStream=="function",Pv=rs&&(typeof TextEncoder=="function"?(i=>r=>i.encode(r))(new TextEncoder):async i=>new Uint8Array(await new Response(i).arrayBuffer())),Od=(i,...r)=>{try{return!!i(...r)}catch{return!1}},Av=Td&&Od(()=>{let i=!1;const r=new Request(rt.origin,{body:new ReadableStream,method:"POST",get duplex(){return i=!0,"half"}}).headers.has("Content-Type");return i&&!r}),Ip=64*1024,pl=Td&&Od(()=>C.isReadableStream(new Response("").body)),Qo={stream:pl&&(i=>i.body)};rs&&(i=>{["text","arrayBuffer","blob","formData","stream"].forEach(r=>{!Qo[r]&&(Qo[r]=C.isFunction(i[r])?o=>o[r]():(o,a)=>{throw new ie(`Response type '${r}' is not supported`,ie.ERR_NOT_SUPPORT,a)})})})(new Response);const Dv=async i=>{if(i==null)return 0;if(C.isBlob(i))return i.size;if(C.isSpecCompliantForm(i))return(await new Request(rt.origin,{method:"POST",body:i}).arrayBuffer()).byteLength;if(C.isArrayBufferView(i)||C.isArrayBuffer(i))return i.byteLength;if(C.isURLSearchParams(i)&&(i=i+""),C.isString(i))return(await Pv(i)).byteLength},zv=async(i,r)=>{const o=C.toFiniteNumber(i.getContentLength());return o??Dv(r)},Lv=rs&&(async i=>{let{url:r,method:o,data:a,signal:u,cancelToken:d,timeout:f,onDownloadProgress:g,onUploadProgress:E,responseType:_,headers:b,withCredentials:N="same-origin",fetchOptions:z}=Rd(i);_=_?(_+"").toLowerCase():"text";let J=jv([u,d&&d.toAbortSignal()],f),D;const L=J&&J.unsubscribe&&(()=>{J.unsubscribe()});let P;try{if(E&&Av&&o!=="get"&&o!=="head"&&(P=await zv(b,a))!==0){let ne=new Request(r,{method:"POST",body:a,duplex:"half"}),ce;if(C.isFormData(a)&&(ce=ne.headers.get("content-type"))&&b.setContentType(ce),ne.body){const[ue,ke]=Dp(P,Ko(zp(E)));a=Fp(ne.body,Ip,ue,ke)}}C.isString(N)||(N=N?"include":"omit");const se="credentials"in Request.prototype;D=new Request(r,{...z,signal:J,method:o.toUpperCase(),headers:b.normalize().toJSON(),body:a,duplex:"half",credentials:se?N:void 0});let Q=await fetch(D,z);const B=pl&&(_==="stream"||_==="response");if(pl&&(g||B&&L)){const ne={};["status","statusText","headers"].forEach(ze=>{ne[ze]=Q[ze]});const ce=C.toFiniteNumber(Q.headers.get("content-length")),[ue,ke]=g&&Dp(ce,Ko(zp(g),!0))||[];Q=new Response(Fp(Q.body,Ip,ue,()=>{ke&&ke(),L&&L()}),ne)}_=_||"text";let U=await Qo[C.findKey(Qo,_)||"text"](Q,i);return!B&&L&&L(),await new Promise((ne,ce)=>{Nd(ne,ce,{data:U,headers:mt.from(Q.headers),status:Q.status,statusText:Q.statusText,config:i,request:D})})}catch(se){throw L&&L(),se&&se.name==="TypeError"&&/Load failed|fetch/i.test(se.message)?Object.assign(new ie("Network Error",ie.ERR_NETWORK,i,D),{cause:se.cause||se}):ie.from(se,se&&se.code,i,D)}}),dl={http:Xh,xhr:Nv,fetch:Lv};C.forEach(dl,(i,r)=>{if(i){try{Object.defineProperty(i,"name",{value:r})}catch{}Object.defineProperty(i,"adapterName",{value:r})}});const Bp=i=>`- ${i}`,Fv=i=>C.isFunction(i)||i===null||i===!1,Pd={getAdapter:i=>{i=C.isArray(i)?i:[i];const{length:r}=i;let o,a;const u={};for(let d=0;d<r;d++){o=i[d];let f;if(a=o,!Fv(o)&&(a=dl[(f=String(o)).toLowerCase()],a===void 0))throw new ie(`Unknown adapter '${f}'`);if(a)break;u[f||"#"+d]=a}if(!a){const d=Object.entries(u).map(([g,E])=>`adapter ${g} `+(E===!1?"is not supported by the environment":"is not available in the build"));let f=r?d.length>1?`since :
`+d.map(Bp).join(`
`):" "+Bp(d[0]):"as no adapter specified";throw new ie("There is no suitable adapter to dispatch the request "+f,"ERR_NOT_SUPPORT")}return a},adapters:dl};function Ga(i){if(i.cancelToken&&i.cancelToken.throwIfRequested(),i.signal&&i.signal.aborted)throw new Nr(null,i)}function Mp(i){return Ga(i),i.headers=mt.from(i.headers),i.data=Xa.call(i,i.transformRequest),["post","put","patch"].indexOf(i.method)!==-1&&i.headers.setContentType("application/x-www-form-urlencoded",!1),Pd.getAdapter(i.adapter||_i.adapter)(i).then(function(a){return Ga(i),a.data=Xa.call(i,i.transformResponse,a),a.headers=mt.from(a.headers),a},function(a){return Cd(a)||(Ga(i),a&&a.response&&(a.response.data=Xa.call(i,i.transformResponse,a.response),a.response.headers=mt.from(a.response.headers))),Promise.reject(a)})}const Ad="1.10.0",is={};["object","boolean","number","function","string","symbol"].forEach((i,r)=>{is[i]=function(a){return typeof a===i||"a"+(r<1?"n ":" ")+i}});const Up={};is.transitional=function(r,o,a){function u(d,f){return"[Axios v"+Ad+"] Transitional option '"+d+"'"+f+(a?". "+a:"")}return(d,f,g)=>{if(r===!1)throw new ie(u(f," has been removed"+(o?" in "+o:"")),ie.ERR_DEPRECATED);return o&&!Up[f]&&(Up[f]=!0,console.warn(u(f," has been deprecated since v"+o+" and will be removed in the near future"))),r?r(d,f,g):!0}};is.spelling=function(r){return(o,a)=>(console.warn(`${a} is likely a misspelling of ${r}`),!0)};function Iv(i,r,o){if(typeof i!="object")throw new ie("options must be an object",ie.ERR_BAD_OPTION_VALUE);const a=Object.keys(i);let u=a.length;for(;u-- >0;){const d=a[u],f=r[d];if(f){const g=i[d],E=g===void 0||f(g,d,i);if(E!==!0)throw new ie("option "+d+" must be "+E,ie.ERR_BAD_OPTION_VALUE);continue}if(o!==!0)throw new ie("Unknown option "+d,ie.ERR_BAD_OPTION)}}const Uo={assertOptions:Iv,validators:is},$t=Uo.validators;let Qn=class{constructor(r){this.defaults=r||{},this.interceptors={request:new Pp,response:new Pp}}async request(r,o){try{return await this._request(r,o)}catch(a){if(a instanceof Error){let u={};Error.captureStackTrace?Error.captureStackTrace(u):u=new Error;const d=u.stack?u.stack.replace(/^.+\n/,""):"";try{a.stack?d&&!String(a.stack).endsWith(d.replace(/^.+\n.+\n/,""))&&(a.stack+=`
`+d):a.stack=d}catch{}}throw a}}_request(r,o){typeof r=="string"?(o=o||{},o.url=r):o=r||{},o=Jn(this.defaults,o);const{transitional:a,paramsSerializer:u,headers:d}=o;a!==void 0&&Uo.assertOptions(a,{silentJSONParsing:$t.transitional($t.boolean),forcedJSONParsing:$t.transitional($t.boolean),clarifyTimeoutError:$t.transitional($t.boolean)},!1),u!=null&&(C.isFunction(u)?o.paramsSerializer={serialize:u}:Uo.assertOptions(u,{encode:$t.function,serialize:$t.function},!0)),o.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?o.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:o.allowAbsoluteUrls=!0),Uo.assertOptions(o,{baseUrl:$t.spelling("baseURL"),withXsrfToken:$t.spelling("withXSRFToken")},!0),o.method=(o.method||this.defaults.method||"get").toLowerCase();let f=d&&C.merge(d.common,d[o.method]);d&&C.forEach(["delete","get","head","post","put","patch","common"],D=>{delete d[D]}),o.headers=mt.concat(f,d);const g=[];let E=!0;this.interceptors.request.forEach(function(L){typeof L.runWhen=="function"&&L.runWhen(o)===!1||(E=E&&L.synchronous,g.unshift(L.fulfilled,L.rejected))});const _=[];this.interceptors.response.forEach(function(L){_.push(L.fulfilled,L.rejected)});let b,N=0,z;if(!E){const D=[Mp.bind(this),void 0];for(D.unshift.apply(D,g),D.push.apply(D,_),z=D.length,b=Promise.resolve(o);N<z;)b=b.then(D[N++],D[N++]);return b}z=g.length;let J=o;for(N=0;N<z;){const D=g[N++],L=g[N++];try{J=D(J)}catch(P){L.call(this,P);break}}try{b=Mp.call(this,J)}catch(D){return Promise.reject(D)}for(N=0,z=_.length;N<z;)b=b.then(_[N++],_[N++]);return b}getUri(r){r=Jn(this.defaults,r);const o=jd(r.baseURL,r.url,r.allowAbsoluteUrls);return Ed(o,r.params,r.paramsSerializer)}};C.forEach(["delete","get","head","options"],function(r){Qn.prototype[r]=function(o,a){return this.request(Jn(a||{},{method:r,url:o,data:(a||{}).data}))}});C.forEach(["post","put","patch"],function(r){function o(a){return function(d,f,g){return this.request(Jn(g||{},{method:r,headers:a?{"Content-Type":"multipart/form-data"}:{},url:d,data:f}))}}Qn.prototype[r]=o(),Qn.prototype[r+"Form"]=o(!0)});let Bv=class Dd{constructor(r){if(typeof r!="function")throw new TypeError("executor must be a function.");let o;this.promise=new Promise(function(d){o=d});const a=this;this.promise.then(u=>{if(!a._listeners)return;let d=a._listeners.length;for(;d-- >0;)a._listeners[d](u);a._listeners=null}),this.promise.then=u=>{let d;const f=new Promise(g=>{a.subscribe(g),d=g}).then(u);return f.cancel=function(){a.unsubscribe(d)},f},r(function(d,f,g){a.reason||(a.reason=new Nr(d,f,g),o(a.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(r){if(this.reason){r(this.reason);return}this._listeners?this._listeners.push(r):this._listeners=[r]}unsubscribe(r){if(!this._listeners)return;const o=this._listeners.indexOf(r);o!==-1&&this._listeners.splice(o,1)}toAbortSignal(){const r=new AbortController,o=a=>{r.abort(a)};return this.subscribe(o),r.signal.unsubscribe=()=>this.unsubscribe(o),r.signal}static source(){let r;return{token:new Dd(function(u){r=u}),cancel:r}}};function Mv(i){return function(o){return i.apply(null,o)}}function Uv(i){return C.isObject(i)&&i.isAxiosError===!0}const fl={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(fl).forEach(([i,r])=>{fl[r]=i});function zd(i){const r=new Qn(i),o=pd(Qn.prototype.request,r);return C.extend(o,Qn.prototype,r,{allOwnKeys:!0}),C.extend(o,r,null,{allOwnKeys:!0}),o.create=function(u){return zd(Jn(i,u))},o}const Ee=zd(_i);Ee.Axios=Qn;Ee.CanceledError=Nr;Ee.CancelToken=Bv;Ee.isCancel=Cd;Ee.VERSION=Ad;Ee.toFormData=ns;Ee.AxiosError=ie;Ee.Cancel=Ee.CanceledError;Ee.all=function(r){return Promise.all(r)};Ee.spread=Mv;Ee.isAxiosError=Uv;Ee.mergeConfig=Jn;Ee.AxiosHeaders=mt;Ee.formToJSON=i=>bd(C.isHTMLForm(i)?new FormData(i):i);Ee.getAdapter=Pd.getAdapter;Ee.HttpStatusCode=fl;Ee.default=Ee;const{Axios:Zy,AxiosError:ex,CanceledError:tx,isCancel:nx,CancelToken:rx,VERSION:ix,all:ox,Cancel:sx,isAxiosError:ax,spread:lx,toFormData:cx,AxiosHeaders:ux,HttpStatusCode:px,formToJSON:dx,getAdapter:fx,mergeConfig:mx}=Ee,Vt=Object.create(null);Vt.open="0";Vt.close="1";Vt.ping="2";Vt.pong="3";Vt.message="4";Vt.upgrade="5";Vt.noop="6";const qo=Object.create(null);Object.keys(Vt).forEach(i=>{qo[Vt[i]]=i});const ml={type:"error",data:"parser error"},Ld=typeof Blob=="function"||typeof Blob<"u"&&Object.prototype.toString.call(Blob)==="[object BlobConstructor]",Fd=typeof ArrayBuffer=="function",Id=i=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(i):i&&i.buffer instanceof ArrayBuffer,Ol=({type:i,data:r},o,a)=>Ld&&r instanceof Blob?o?a(r):qp(r,a):Fd&&(r instanceof ArrayBuffer||Id(r))?o?a(r):qp(new Blob([r]),a):a(Vt[i]+(r||"")),qp=(i,r)=>{const o=new FileReader;return o.onload=function(){const a=o.result.split(",")[1];r("b"+(a||""))},o.readAsDataURL(i)};function $p(i){return i instanceof Uint8Array?i:i instanceof ArrayBuffer?new Uint8Array(i):new Uint8Array(i.buffer,i.byteOffset,i.byteLength)}let Za;function qv(i,r){if(Ld&&i.data instanceof Blob)return i.data.arrayBuffer().then($p).then(r);if(Fd&&(i.data instanceof ArrayBuffer||Id(i.data)))return r($p(i.data));Ol(i,!1,o=>{Za||(Za=new TextEncoder),r(Za.encode(o))})}const Hp="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",wi=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(let i=0;i<Hp.length;i++)wi[Hp.charCodeAt(i)]=i;const $v=i=>{let r=i.length*.75,o=i.length,a,u=0,d,f,g,E;i[i.length-1]==="="&&(r--,i[i.length-2]==="="&&r--);const _=new ArrayBuffer(r),b=new Uint8Array(_);for(a=0;a<o;a+=4)d=wi[i.charCodeAt(a)],f=wi[i.charCodeAt(a+1)],g=wi[i.charCodeAt(a+2)],E=wi[i.charCodeAt(a+3)],b[u++]=d<<2|f>>4,b[u++]=(f&15)<<4|g>>2,b[u++]=(g&3)<<6|E&63;return _},Hv=typeof ArrayBuffer=="function",Pl=(i,r)=>{if(typeof i!="string")return{type:"message",data:Bd(i,r)};const o=i.charAt(0);return o==="b"?{type:"message",data:Vv(i.substring(1),r)}:qo[o]?i.length>1?{type:qo[o],data:i.substring(1)}:{type:qo[o]}:ml},Vv=(i,r)=>{if(Hv){const o=$v(i);return Bd(o,r)}else return{base64:!0,data:i}},Bd=(i,r)=>{switch(r){case"blob":return i instanceof Blob?i:new Blob([i]);case"arraybuffer":default:return i instanceof ArrayBuffer?i:i.buffer}},Md="",Wv=(i,r)=>{const o=i.length,a=new Array(o);let u=0;i.forEach((d,f)=>{Ol(d,!1,g=>{a[f]=g,++u===o&&r(a.join(Md))})})},Kv=(i,r)=>{const o=i.split(Md),a=[];for(let u=0;u<o.length;u++){const d=Pl(o[u],r);if(a.push(d),d.type==="error")break}return a};function Qv(){return new TransformStream({transform(i,r){qv(i,o=>{const a=o.length;let u;if(a<126)u=new Uint8Array(1),new DataView(u.buffer).setUint8(0,a);else if(a<65536){u=new Uint8Array(3);const d=new DataView(u.buffer);d.setUint8(0,126),d.setUint16(1,a)}else{u=new Uint8Array(9);const d=new DataView(u.buffer);d.setUint8(0,127),d.setBigUint64(1,BigInt(a))}i.data&&typeof i.data!="string"&&(u[0]|=128),r.enqueue(u),r.enqueue(o)})}})}let el;function zo(i){return i.reduce((r,o)=>r+o.length,0)}function Lo(i,r){if(i[0].length===r)return i.shift();const o=new Uint8Array(r);let a=0;for(let u=0;u<r;u++)o[u]=i[0][a++],a===i[0].length&&(i.shift(),a=0);return i.length&&a<i[0].length&&(i[0]=i[0].slice(a)),o}function Jv(i,r){el||(el=new TextDecoder);const o=[];let a=0,u=-1,d=!1;return new TransformStream({transform(f,g){for(o.push(f);;){if(a===0){if(zo(o)<1)break;const E=Lo(o,1);d=(E[0]&128)===128,u=E[0]&127,u<126?a=3:u===126?a=1:a=2}else if(a===1){if(zo(o)<2)break;const E=Lo(o,2);u=new DataView(E.buffer,E.byteOffset,E.length).getUint16(0),a=3}else if(a===2){if(zo(o)<8)break;const E=Lo(o,8),_=new DataView(E.buffer,E.byteOffset,E.length),b=_.getUint32(0);if(b>Math.pow(2,21)-1){g.enqueue(ml);break}u=b*Math.pow(2,32)+_.getUint32(4),a=3}else{if(zo(o)<u)break;const E=Lo(o,u);g.enqueue(Pl(d?E:el.decode(E),r)),a=0}if(u===0||u>i){g.enqueue(ml);break}}}})}const Ud=4;function Ue(i){if(i)return Yv(i)}function Yv(i){for(var r in Ue.prototype)i[r]=Ue.prototype[r];return i}Ue.prototype.on=Ue.prototype.addEventListener=function(i,r){return this._callbacks=this._callbacks||{},(this._callbacks["$"+i]=this._callbacks["$"+i]||[]).push(r),this};Ue.prototype.once=function(i,r){function o(){this.off(i,o),r.apply(this,arguments)}return o.fn=r,this.on(i,o),this};Ue.prototype.off=Ue.prototype.removeListener=Ue.prototype.removeAllListeners=Ue.prototype.removeEventListener=function(i,r){if(this._callbacks=this._callbacks||{},arguments.length==0)return this._callbacks={},this;var o=this._callbacks["$"+i];if(!o)return this;if(arguments.length==1)return delete this._callbacks["$"+i],this;for(var a,u=0;u<o.length;u++)if(a=o[u],a===r||a.fn===r){o.splice(u,1);break}return o.length===0&&delete this._callbacks["$"+i],this};Ue.prototype.emit=function(i){this._callbacks=this._callbacks||{};for(var r=new Array(arguments.length-1),o=this._callbacks["$"+i],a=1;a<arguments.length;a++)r[a-1]=arguments[a];if(o){o=o.slice(0);for(var a=0,u=o.length;a<u;++a)o[a].apply(this,r)}return this};Ue.prototype.emitReserved=Ue.prototype.emit;Ue.prototype.listeners=function(i){return this._callbacks=this._callbacks||{},this._callbacks["$"+i]||[]};Ue.prototype.hasListeners=function(i){return!!this.listeners(i).length};const os=typeof Promise=="function"&&typeof Promise.resolve=="function"?r=>Promise.resolve().then(r):(r,o)=>o(r,0),Ct=typeof self<"u"?self:typeof window<"u"?window:Function("return this")(),Xv="arraybuffer";function qd(i,...r){return r.reduce((o,a)=>(i.hasOwnProperty(a)&&(o[a]=i[a]),o),{})}const Gv=Ct.setTimeout,Zv=Ct.clearTimeout;function ss(i,r){r.useNativeTimers?(i.setTimeoutFn=Gv.bind(Ct),i.clearTimeoutFn=Zv.bind(Ct)):(i.setTimeoutFn=Ct.setTimeout.bind(Ct),i.clearTimeoutFn=Ct.clearTimeout.bind(Ct))}const eg=1.33;function tg(i){return typeof i=="string"?ng(i):Math.ceil((i.byteLength||i.size)*eg)}function ng(i){let r=0,o=0;for(let a=0,u=i.length;a<u;a++)r=i.charCodeAt(a),r<128?o+=1:r<2048?o+=2:r<55296||r>=57344?o+=3:(a++,o+=4);return o}function $d(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}function rg(i){let r="";for(let o in i)i.hasOwnProperty(o)&&(r.length&&(r+="&"),r+=encodeURIComponent(o)+"="+encodeURIComponent(i[o]));return r}function ig(i){let r={},o=i.split("&");for(let a=0,u=o.length;a<u;a++){let d=o[a].split("=");r[decodeURIComponent(d[0])]=decodeURIComponent(d[1])}return r}class og extends Error{constructor(r,o,a){super(r),this.description=o,this.context=a,this.type="TransportError"}}class Al extends Ue{constructor(r){super(),this.writable=!1,ss(this,r),this.opts=r,this.query=r.query,this.socket=r.socket,this.supportsBinary=!r.forceBase64}onError(r,o,a){return super.emitReserved("error",new og(r,o,a)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return(this.readyState==="opening"||this.readyState==="open")&&(this.doClose(),this.onClose()),this}send(r){this.readyState==="open"&&this.write(r)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(r){const o=Pl(r,this.socket.binaryType);this.onPacket(o)}onPacket(r){super.emitReserved("packet",r)}onClose(r){this.readyState="closed",super.emitReserved("close",r)}pause(r){}createUri(r,o={}){return r+"://"+this._hostname()+this._port()+this.opts.path+this._query(o)}_hostname(){const r=this.opts.hostname;return r.indexOf(":")===-1?r:"["+r+"]"}_port(){return this.opts.port&&(this.opts.secure&&+(this.opts.port!==443)||!this.opts.secure&&Number(this.opts.port)!==80)?":"+this.opts.port:""}_query(r){const o=rg(r);return o.length?"?"+o:""}}class sg extends Al{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(r){this.readyState="pausing";const o=()=>{this.readyState="paused",r()};if(this._polling||!this.writable){let a=0;this._polling&&(a++,this.once("pollComplete",function(){--a||o()})),this.writable||(a++,this.once("drain",function(){--a||o()}))}else o()}_poll(){this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(r){const o=a=>{if(this.readyState==="opening"&&a.type==="open"&&this.onOpen(),a.type==="close")return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(a)};Kv(r,this.socket.binaryType).forEach(o),this.readyState!=="closed"&&(this._polling=!1,this.emitReserved("pollComplete"),this.readyState==="open"&&this._poll())}doClose(){const r=()=>{this.write([{type:"close"}])};this.readyState==="open"?r():this.once("open",r)}write(r){this.writable=!1,Wv(r,o=>{this.doWrite(o,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){const r=this.opts.secure?"https":"http",o=this.query||{};return this.opts.timestampRequests!==!1&&(o[this.opts.timestampParam]=$d()),!this.supportsBinary&&!o.sid&&(o.b64=1),this.createUri(r,o)}}let Hd=!1;try{Hd=typeof XMLHttpRequest<"u"&&"withCredentials"in new XMLHttpRequest}catch{}const ag=Hd;function lg(){}class cg extends sg{constructor(r){if(super(r),typeof location<"u"){const o=location.protocol==="https:";let a=location.port;a||(a=o?"443":"80"),this.xd=typeof location<"u"&&r.hostname!==location.hostname||a!==r.port}}doWrite(r,o){const a=this.request({method:"POST",data:r});a.on("success",o),a.on("error",(u,d)=>{this.onError("xhr post error",u,d)})}doPoll(){const r=this.request();r.on("data",this.onData.bind(this)),r.on("error",(o,a)=>{this.onError("xhr poll error",o,a)}),this.pollXhr=r}}let _r=class $o extends Ue{constructor(r,o,a){super(),this.createRequest=r,ss(this,a),this._opts=a,this._method=a.method||"GET",this._uri=o,this._data=a.data!==void 0?a.data:null,this._create()}_create(){var r;const o=qd(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");o.xdomain=!!this._opts.xd;const a=this._xhr=this.createRequest(o);try{a.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders){a.setDisableHeaderCheck&&a.setDisableHeaderCheck(!0);for(let u in this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(u)&&a.setRequestHeader(u,this._opts.extraHeaders[u])}}catch{}if(this._method==="POST")try{a.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch{}try{a.setRequestHeader("Accept","*/*")}catch{}(r=this._opts.cookieJar)===null||r===void 0||r.addCookies(a),"withCredentials"in a&&(a.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(a.timeout=this._opts.requestTimeout),a.onreadystatechange=()=>{var u;a.readyState===3&&((u=this._opts.cookieJar)===null||u===void 0||u.parseCookies(a.getResponseHeader("set-cookie"))),a.readyState===4&&(a.status===200||a.status===1223?this._onLoad():this.setTimeoutFn(()=>{this._onError(typeof a.status=="number"?a.status:0)},0))},a.send(this._data)}catch(u){this.setTimeoutFn(()=>{this._onError(u)},0);return}typeof document<"u"&&(this._index=$o.requestsCount++,$o.requests[this._index]=this)}_onError(r){this.emitReserved("error",r,this._xhr),this._cleanup(!0)}_cleanup(r){if(!(typeof this._xhr>"u"||this._xhr===null)){if(this._xhr.onreadystatechange=lg,r)try{this._xhr.abort()}catch{}typeof document<"u"&&delete $o.requests[this._index],this._xhr=null}}_onLoad(){const r=this._xhr.responseText;r!==null&&(this.emitReserved("data",r),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}};_r.requestsCount=0;_r.requests={};if(typeof document<"u"){if(typeof attachEvent=="function")attachEvent("onunload",Vp);else if(typeof addEventListener=="function"){const i="onpagehide"in Ct?"pagehide":"unload";addEventListener(i,Vp,!1)}}function Vp(){for(let i in _r.requests)_r.requests.hasOwnProperty(i)&&_r.requests[i].abort()}const ug=function(){const i=Vd({xdomain:!1});return i&&i.responseType!==null}();class pg extends cg{constructor(r){super(r);const o=r&&r.forceBase64;this.supportsBinary=ug&&!o}request(r={}){return Object.assign(r,{xd:this.xd},this.opts),new _r(Vd,this.uri(),r)}}function Vd(i){const r=i.xdomain;try{if(typeof XMLHttpRequest<"u"&&(!r||ag))return new XMLHttpRequest}catch{}if(!r)try{return new Ct[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch{}}const Wd=typeof navigator<"u"&&typeof navigator.product=="string"&&navigator.product.toLowerCase()==="reactnative";class dg extends Al{get name(){return"websocket"}doOpen(){const r=this.uri(),o=this.opts.protocols,a=Wd?{}:qd(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(a.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(r,o,a)}catch(u){return this.emitReserved("error",u)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=r=>this.onClose({description:"websocket connection closed",context:r}),this.ws.onmessage=r=>this.onData(r.data),this.ws.onerror=r=>this.onError("websocket error",r)}write(r){this.writable=!1;for(let o=0;o<r.length;o++){const a=r[o],u=o===r.length-1;Ol(a,this.supportsBinary,d=>{try{this.doWrite(a,d)}catch{}u&&os(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){typeof this.ws<"u"&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){const r=this.opts.secure?"wss":"ws",o=this.query||{};return this.opts.timestampRequests&&(o[this.opts.timestampParam]=$d()),this.supportsBinary||(o.b64=1),this.createUri(r,o)}}const tl=Ct.WebSocket||Ct.MozWebSocket;class fg extends dg{createSocket(r,o,a){return Wd?new tl(r,o,a):o?new tl(r,o):new tl(r)}doWrite(r,o){this.ws.send(o)}}class mg extends Al{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(r){return this.emitReserved("error",r)}this._transport.closed.then(()=>{this.onClose()}).catch(r=>{this.onError("webtransport error",r)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(r=>{const o=Jv(Number.MAX_SAFE_INTEGER,this.socket.binaryType),a=r.readable.pipeThrough(o).getReader(),u=Qv();u.readable.pipeTo(r.writable),this._writer=u.writable.getWriter();const d=()=>{a.read().then(({done:g,value:E})=>{g||(this.onPacket(E),d())}).catch(g=>{})};d();const f={type:"open"};this.query.sid&&(f.data=`{"sid":"${this.query.sid}"}`),this._writer.write(f).then(()=>this.onOpen())})})}write(r){this.writable=!1;for(let o=0;o<r.length;o++){const a=r[o],u=o===r.length-1;this._writer.write(a).then(()=>{u&&os(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var r;(r=this._transport)===null||r===void 0||r.close()}}const hg={websocket:fg,webtransport:mg,polling:pg},vg=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,gg=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function hl(i){if(i.length>8e3)throw"URI too long";const r=i,o=i.indexOf("["),a=i.indexOf("]");o!=-1&&a!=-1&&(i=i.substring(0,o)+i.substring(o,a).replace(/:/g,";")+i.substring(a,i.length));let u=vg.exec(i||""),d={},f=14;for(;f--;)d[gg[f]]=u[f]||"";return o!=-1&&a!=-1&&(d.source=r,d.host=d.host.substring(1,d.host.length-1).replace(/;/g,":"),d.authority=d.authority.replace("[","").replace("]","").replace(/;/g,":"),d.ipv6uri=!0),d.pathNames=yg(d,d.path),d.queryKey=xg(d,d.query),d}function yg(i,r){const o=/\/{2,9}/g,a=r.replace(o,"/").split("/");return(r.slice(0,1)=="/"||r.length===0)&&a.splice(0,1),r.slice(-1)=="/"&&a.splice(a.length-1,1),a}function xg(i,r){const o={};return r.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(a,u,d){u&&(o[u]=d)}),o}const vl=typeof addEventListener=="function"&&typeof removeEventListener=="function",Ho=[];vl&&addEventListener("offline",()=>{Ho.forEach(i=>i())},!1);class Cn extends Ue{constructor(r,o){if(super(),this.binaryType=Xv,this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,r&&typeof r=="object"&&(o=r,r=null),r){const a=hl(r);o.hostname=a.host,o.secure=a.protocol==="https"||a.protocol==="wss",o.port=a.port,a.query&&(o.query=a.query)}else o.host&&(o.hostname=hl(o.host).host);ss(this,o),this.secure=o.secure!=null?o.secure:typeof location<"u"&&location.protocol==="https:",o.hostname&&!o.port&&(o.port=this.secure?"443":"80"),this.hostname=o.hostname||(typeof location<"u"?location.hostname:"localhost"),this.port=o.port||(typeof location<"u"&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},o.transports.forEach(a=>{const u=a.prototype.name;this.transports.push(u),this._transportsByName[u]=a}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},o),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),typeof this.opts.query=="string"&&(this.opts.query=ig(this.opts.query)),vl&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),this.hostname!=="localhost"&&(this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},Ho.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=void 0),this._open()}createTransport(r){const o=Object.assign({},this.opts.query);o.EIO=Ud,o.transport=r,this.id&&(o.sid=this.id);const a=Object.assign({},this.opts,{query:o,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[r]);return new this._transportsByName[r](a)}_open(){if(this.transports.length===0){this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);return}const r=this.opts.rememberUpgrade&&Cn.priorWebsocketSuccess&&this.transports.indexOf("websocket")!==-1?"websocket":this.transports[0];this.readyState="opening";const o=this.createTransport(r);o.open(),this.setTransport(o)}setTransport(r){this.transport&&this.transport.removeAllListeners(),this.transport=r,r.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",o=>this._onClose("transport close",o))}onOpen(){this.readyState="open",Cn.priorWebsocketSuccess=this.transport.name==="websocket",this.emitReserved("open"),this.flush()}_onPacket(r){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing")switch(this.emitReserved("packet",r),this.emitReserved("heartbeat"),r.type){case"open":this.onHandshake(JSON.parse(r.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":const o=new Error("server error");o.code=r.data,this._onError(o);break;case"message":this.emitReserved("data",r.data),this.emitReserved("message",r.data);break}}onHandshake(r){this.emitReserved("handshake",r),this.id=r.sid,this.transport.query.sid=r.sid,this._pingInterval=r.pingInterval,this._pingTimeout=r.pingTimeout,this._maxPayload=r.maxPayload,this.onOpen(),this.readyState!=="closed"&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);const r=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+r,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},r),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,this.writeBuffer.length===0?this.emitReserved("drain"):this.flush()}flush(){if(this.readyState!=="closed"&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){const r=this._getWritablePackets();this.transport.send(r),this._prevBufferLen=r.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&this.transport.name==="polling"&&this.writeBuffer.length>1))return this.writeBuffer;let o=1;for(let a=0;a<this.writeBuffer.length;a++){const u=this.writeBuffer[a].data;if(u&&(o+=tg(u)),a>0&&o>this._maxPayload)return this.writeBuffer.slice(0,a);o+=2}return this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;const r=Date.now()>this._pingTimeoutTime;return r&&(this._pingTimeoutTime=0,os(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),r}write(r,o,a){return this._sendPacket("message",r,o,a),this}send(r,o,a){return this._sendPacket("message",r,o,a),this}_sendPacket(r,o,a,u){if(typeof o=="function"&&(u=o,o=void 0),typeof a=="function"&&(u=a,a=null),this.readyState==="closing"||this.readyState==="closed")return;a=a||{},a.compress=a.compress!==!1;const d={type:r,data:o,options:a};this.emitReserved("packetCreate",d),this.writeBuffer.push(d),u&&this.once("flush",u),this.flush()}close(){const r=()=>{this._onClose("forced close"),this.transport.close()},o=()=>{this.off("upgrade",o),this.off("upgradeError",o),r()},a=()=>{this.once("upgrade",o),this.once("upgradeError",o)};return(this.readyState==="opening"||this.readyState==="open")&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?a():r()}):this.upgrading?a():r()),this}_onError(r){if(Cn.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&this.readyState==="opening")return this.transports.shift(),this._open();this.emitReserved("error",r),this._onClose("transport error",r)}_onClose(r,o){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing"){if(this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),vl&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){const a=Ho.indexOf(this._offlineEventListener);a!==-1&&Ho.splice(a,1)}this.readyState="closed",this.id=null,this.emitReserved("close",r,o),this.writeBuffer=[],this._prevBufferLen=0}}}Cn.protocol=Ud;class wg extends Cn{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),this.readyState==="open"&&this.opts.upgrade)for(let r=0;r<this._upgrades.length;r++)this._probe(this._upgrades[r])}_probe(r){let o=this.createTransport(r),a=!1;Cn.priorWebsocketSuccess=!1;const u=()=>{a||(o.send([{type:"ping",data:"probe"}]),o.once("packet",N=>{if(!a)if(N.type==="pong"&&N.data==="probe"){if(this.upgrading=!0,this.emitReserved("upgrading",o),!o)return;Cn.priorWebsocketSuccess=o.name==="websocket",this.transport.pause(()=>{a||this.readyState!=="closed"&&(b(),this.setTransport(o),o.send([{type:"upgrade"}]),this.emitReserved("upgrade",o),o=null,this.upgrading=!1,this.flush())})}else{const z=new Error("probe error");z.transport=o.name,this.emitReserved("upgradeError",z)}}))};function d(){a||(a=!0,b(),o.close(),o=null)}const f=N=>{const z=new Error("probe error: "+N);z.transport=o.name,d(),this.emitReserved("upgradeError",z)};function g(){f("transport closed")}function E(){f("socket closed")}function _(N){o&&N.name!==o.name&&d()}const b=()=>{o.removeListener("open",u),o.removeListener("error",f),o.removeListener("close",g),this.off("close",E),this.off("upgrading",_)};o.once("open",u),o.once("error",f),o.once("close",g),this.once("close",E),this.once("upgrading",_),this._upgrades.indexOf("webtransport")!==-1&&r!=="webtransport"?this.setTimeoutFn(()=>{a||o.open()},200):o.open()}onHandshake(r){this._upgrades=this._filterUpgrades(r.upgrades),super.onHandshake(r)}_filterUpgrades(r){const o=[];for(let a=0;a<r.length;a++)~this.transports.indexOf(r[a])&&o.push(r[a]);return o}}let kg=class extends wg{constructor(r,o={}){const a=typeof r=="object"?r:o;(!a.transports||a.transports&&typeof a.transports[0]=="string")&&(a.transports=(a.transports||["polling","websocket","webtransport"]).map(u=>hg[u]).filter(u=>!!u)),super(r,a)}};function Sg(i,r="",o){let a=i;o=o||typeof location<"u"&&location,i==null&&(i=o.protocol+"//"+o.host),typeof i=="string"&&(i.charAt(0)==="/"&&(i.charAt(1)==="/"?i=o.protocol+i:i=o.host+i),/^(https?|wss?):\/\//.test(i)||(typeof o<"u"?i=o.protocol+"//"+i:i="https://"+i),a=hl(i)),a.port||(/^(http|ws)$/.test(a.protocol)?a.port="80":/^(http|ws)s$/.test(a.protocol)&&(a.port="443")),a.path=a.path||"/";const d=a.host.indexOf(":")!==-1?"["+a.host+"]":a.host;return a.id=a.protocol+"://"+d+":"+a.port+r,a.href=a.protocol+"://"+d+(o&&o.port===a.port?"":":"+a.port),a}const Eg=typeof ArrayBuffer=="function",_g=i=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(i):i.buffer instanceof ArrayBuffer,Kd=Object.prototype.toString,bg=typeof Blob=="function"||typeof Blob<"u"&&Kd.call(Blob)==="[object BlobConstructor]",Cg=typeof File=="function"||typeof File<"u"&&Kd.call(File)==="[object FileConstructor]";function Dl(i){return Eg&&(i instanceof ArrayBuffer||_g(i))||bg&&i instanceof Blob||Cg&&i instanceof File}function Vo(i,r){if(!i||typeof i!="object")return!1;if(Array.isArray(i)){for(let o=0,a=i.length;o<a;o++)if(Vo(i[o]))return!0;return!1}if(Dl(i))return!0;if(i.toJSON&&typeof i.toJSON=="function"&&arguments.length===1)return Vo(i.toJSON(),!0);for(const o in i)if(Object.prototype.hasOwnProperty.call(i,o)&&Vo(i[o]))return!0;return!1}function Ng(i){const r=[],o=i.data,a=i;return a.data=gl(o,r),a.attachments=r.length,{packet:a,buffers:r}}function gl(i,r){if(!i)return i;if(Dl(i)){const o={_placeholder:!0,num:r.length};return r.push(i),o}else if(Array.isArray(i)){const o=new Array(i.length);for(let a=0;a<i.length;a++)o[a]=gl(i[a],r);return o}else if(typeof i=="object"&&!(i instanceof Date)){const o={};for(const a in i)Object.prototype.hasOwnProperty.call(i,a)&&(o[a]=gl(i[a],r));return o}return i}function jg(i,r){return i.data=yl(i.data,r),delete i.attachments,i}function yl(i,r){if(!i)return i;if(i&&i._placeholder===!0){if(typeof i.num=="number"&&i.num>=0&&i.num<r.length)return r[i.num];throw new Error("illegal attachments")}else if(Array.isArray(i))for(let o=0;o<i.length;o++)i[o]=yl(i[o],r);else if(typeof i=="object")for(const o in i)Object.prototype.hasOwnProperty.call(i,o)&&(i[o]=yl(i[o],r));return i}const Rg=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],Tg=5;var de;(function(i){i[i.CONNECT=0]="CONNECT",i[i.DISCONNECT=1]="DISCONNECT",i[i.EVENT=2]="EVENT",i[i.ACK=3]="ACK",i[i.CONNECT_ERROR=4]="CONNECT_ERROR",i[i.BINARY_EVENT=5]="BINARY_EVENT",i[i.BINARY_ACK=6]="BINARY_ACK"})(de||(de={}));class Og{constructor(r){this.replacer=r}encode(r){return(r.type===de.EVENT||r.type===de.ACK)&&Vo(r)?this.encodeAsBinary({type:r.type===de.EVENT?de.BINARY_EVENT:de.BINARY_ACK,nsp:r.nsp,data:r.data,id:r.id}):[this.encodeAsString(r)]}encodeAsString(r){let o=""+r.type;return(r.type===de.BINARY_EVENT||r.type===de.BINARY_ACK)&&(o+=r.attachments+"-"),r.nsp&&r.nsp!=="/"&&(o+=r.nsp+","),r.id!=null&&(o+=r.id),r.data!=null&&(o+=JSON.stringify(r.data,this.replacer)),o}encodeAsBinary(r){const o=Ng(r),a=this.encodeAsString(o.packet),u=o.buffers;return u.unshift(a),u}}function Wp(i){return Object.prototype.toString.call(i)==="[object Object]"}class zl extends Ue{constructor(r){super(),this.reviver=r}add(r){let o;if(typeof r=="string"){if(this.reconstructor)throw new Error("got plaintext data when reconstructing a packet");o=this.decodeString(r);const a=o.type===de.BINARY_EVENT;a||o.type===de.BINARY_ACK?(o.type=a?de.EVENT:de.ACK,this.reconstructor=new Pg(o),o.attachments===0&&super.emitReserved("decoded",o)):super.emitReserved("decoded",o)}else if(Dl(r)||r.base64)if(this.reconstructor)o=this.reconstructor.takeBinaryData(r),o&&(this.reconstructor=null,super.emitReserved("decoded",o));else throw new Error("got binary data when not reconstructing a packet");else throw new Error("Unknown type: "+r)}decodeString(r){let o=0;const a={type:Number(r.charAt(0))};if(de[a.type]===void 0)throw new Error("unknown packet type "+a.type);if(a.type===de.BINARY_EVENT||a.type===de.BINARY_ACK){const d=o+1;for(;r.charAt(++o)!=="-"&&o!=r.length;);const f=r.substring(d,o);if(f!=Number(f)||r.charAt(o)!=="-")throw new Error("Illegal attachments");a.attachments=Number(f)}if(r.charAt(o+1)==="/"){const d=o+1;for(;++o&&!(r.charAt(o)===","||o===r.length););a.nsp=r.substring(d,o)}else a.nsp="/";const u=r.charAt(o+1);if(u!==""&&Number(u)==u){const d=o+1;for(;++o;){const f=r.charAt(o);if(f==null||Number(f)!=f){--o;break}if(o===r.length)break}a.id=Number(r.substring(d,o+1))}if(r.charAt(++o)){const d=this.tryParse(r.substr(o));if(zl.isPayloadValid(a.type,d))a.data=d;else throw new Error("invalid payload")}return a}tryParse(r){try{return JSON.parse(r,this.reviver)}catch{return!1}}static isPayloadValid(r,o){switch(r){case de.CONNECT:return Wp(o);case de.DISCONNECT:return o===void 0;case de.CONNECT_ERROR:return typeof o=="string"||Wp(o);case de.EVENT:case de.BINARY_EVENT:return Array.isArray(o)&&(typeof o[0]=="number"||typeof o[0]=="string"&&Rg.indexOf(o[0])===-1);case de.ACK:case de.BINARY_ACK:return Array.isArray(o)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class Pg{constructor(r){this.packet=r,this.buffers=[],this.reconPack=r}takeBinaryData(r){if(this.buffers.push(r),this.buffers.length===this.reconPack.attachments){const o=jg(this.reconPack,this.buffers);return this.finishedReconstruction(),o}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}const Ag=Object.freeze(Object.defineProperty({__proto__:null,Decoder:zl,Encoder:Og,get PacketType(){return de},protocol:Tg},Symbol.toStringTag,{value:"Module"}));function Dt(i,r,o){return i.on(r,o),function(){i.off(r,o)}}const Dg=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class Qd extends Ue{constructor(r,o,a){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=r,this.nsp=o,a&&a.auth&&(this.auth=a.auth),this._opts=Object.assign({},a),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;const r=this.io;this.subs=[Dt(r,"open",this.onopen.bind(this)),Dt(r,"packet",this.onpacket.bind(this)),Dt(r,"error",this.onerror.bind(this)),Dt(r,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected?this:(this.subEvents(),this.io._reconnecting||this.io.open(),this.io._readyState==="open"&&this.onopen(),this)}open(){return this.connect()}send(...r){return r.unshift("message"),this.emit.apply(this,r),this}emit(r,...o){var a,u,d;if(Dg.hasOwnProperty(r))throw new Error('"'+r.toString()+'" is a reserved event name');if(o.unshift(r),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(o),this;const f={type:de.EVENT,data:o};if(f.options={},f.options.compress=this.flags.compress!==!1,typeof o[o.length-1]=="function"){const b=this.ids++,N=o.pop();this._registerAckCallback(b,N),f.id=b}const g=(u=(a=this.io.engine)===null||a===void 0?void 0:a.transport)===null||u===void 0?void 0:u.writable,E=this.connected&&!(!((d=this.io.engine)===null||d===void 0)&&d._hasPingExpired());return this.flags.volatile&&!g||(E?(this.notifyOutgoingListeners(f),this.packet(f)):this.sendBuffer.push(f)),this.flags={},this}_registerAckCallback(r,o){var a;const u=(a=this.flags.timeout)!==null&&a!==void 0?a:this._opts.ackTimeout;if(u===void 0){this.acks[r]=o;return}const d=this.io.setTimeoutFn(()=>{delete this.acks[r];for(let g=0;g<this.sendBuffer.length;g++)this.sendBuffer[g].id===r&&this.sendBuffer.splice(g,1);o.call(this,new Error("operation has timed out"))},u),f=(...g)=>{this.io.clearTimeoutFn(d),o.apply(this,g)};f.withError=!0,this.acks[r]=f}emitWithAck(r,...o){return new Promise((a,u)=>{const d=(f,g)=>f?u(f):a(g);d.withError=!0,o.push(d),this.emit(r,...o)})}_addToQueue(r){let o;typeof r[r.length-1]=="function"&&(o=r.pop());const a={id:this._queueSeq++,tryCount:0,pending:!1,args:r,flags:Object.assign({fromQueue:!0},this.flags)};r.push((u,...d)=>a!==this._queue[0]?void 0:(u!==null?a.tryCount>this._opts.retries&&(this._queue.shift(),o&&o(u)):(this._queue.shift(),o&&o(null,...d)),a.pending=!1,this._drainQueue())),this._queue.push(a),this._drainQueue()}_drainQueue(r=!1){if(!this.connected||this._queue.length===0)return;const o=this._queue[0];o.pending&&!r||(o.pending=!0,o.tryCount++,this.flags=o.flags,this.emit.apply(this,o.args))}packet(r){r.nsp=this.nsp,this.io._packet(r)}onopen(){typeof this.auth=="function"?this.auth(r=>{this._sendConnectPacket(r)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(r){this.packet({type:de.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},r):r})}onerror(r){this.connected||this.emitReserved("connect_error",r)}onclose(r,o){this.connected=!1,delete this.id,this.emitReserved("disconnect",r,o),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(r=>{if(!this.sendBuffer.some(a=>String(a.id)===r)){const a=this.acks[r];delete this.acks[r],a.withError&&a.call(this,new Error("socket has been disconnected"))}})}onpacket(r){if(r.nsp===this.nsp)switch(r.type){case de.CONNECT:r.data&&r.data.sid?this.onconnect(r.data.sid,r.data.pid):this.emitReserved("connect_error",new Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case de.EVENT:case de.BINARY_EVENT:this.onevent(r);break;case de.ACK:case de.BINARY_ACK:this.onack(r);break;case de.DISCONNECT:this.ondisconnect();break;case de.CONNECT_ERROR:this.destroy();const a=new Error(r.data.message);a.data=r.data.data,this.emitReserved("connect_error",a);break}}onevent(r){const o=r.data||[];r.id!=null&&o.push(this.ack(r.id)),this.connected?this.emitEvent(o):this.receiveBuffer.push(Object.freeze(o))}emitEvent(r){if(this._anyListeners&&this._anyListeners.length){const o=this._anyListeners.slice();for(const a of o)a.apply(this,r)}super.emit.apply(this,r),this._pid&&r.length&&typeof r[r.length-1]=="string"&&(this._lastOffset=r[r.length-1])}ack(r){const o=this;let a=!1;return function(...u){a||(a=!0,o.packet({type:de.ACK,id:r,data:u}))}}onack(r){const o=this.acks[r.id];typeof o=="function"&&(delete this.acks[r.id],o.withError&&r.data.unshift(null),o.apply(this,r.data))}onconnect(r,o){this.id=r,this.recovered=o&&this._pid===o,this._pid=o,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(r=>this.emitEvent(r)),this.receiveBuffer=[],this.sendBuffer.forEach(r=>{this.notifyOutgoingListeners(r),this.packet(r)}),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(r=>r()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:de.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(r){return this.flags.compress=r,this}get volatile(){return this.flags.volatile=!0,this}timeout(r){return this.flags.timeout=r,this}onAny(r){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(r),this}prependAny(r){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(r),this}offAny(r){if(!this._anyListeners)return this;if(r){const o=this._anyListeners;for(let a=0;a<o.length;a++)if(r===o[a])return o.splice(a,1),this}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(r){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(r),this}prependAnyOutgoing(r){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(r),this}offAnyOutgoing(r){if(!this._anyOutgoingListeners)return this;if(r){const o=this._anyOutgoingListeners;for(let a=0;a<o.length;a++)if(r===o[a])return o.splice(a,1),this}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(r){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length){const o=this._anyOutgoingListeners.slice();for(const a of o)a.apply(this,r.data)}}}function jr(i){i=i||{},this.ms=i.min||100,this.max=i.max||1e4,this.factor=i.factor||2,this.jitter=i.jitter>0&&i.jitter<=1?i.jitter:0,this.attempts=0}jr.prototype.duration=function(){var i=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var r=Math.random(),o=Math.floor(r*this.jitter*i);i=(Math.floor(r*10)&1)==0?i-o:i+o}return Math.min(i,this.max)|0};jr.prototype.reset=function(){this.attempts=0};jr.prototype.setMin=function(i){this.ms=i};jr.prototype.setMax=function(i){this.max=i};jr.prototype.setJitter=function(i){this.jitter=i};class xl extends Ue{constructor(r,o){var a;super(),this.nsps={},this.subs=[],r&&typeof r=="object"&&(o=r,r=void 0),o=o||{},o.path=o.path||"/socket.io",this.opts=o,ss(this,o),this.reconnection(o.reconnection!==!1),this.reconnectionAttempts(o.reconnectionAttempts||1/0),this.reconnectionDelay(o.reconnectionDelay||1e3),this.reconnectionDelayMax(o.reconnectionDelayMax||5e3),this.randomizationFactor((a=o.randomizationFactor)!==null&&a!==void 0?a:.5),this.backoff=new jr({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(o.timeout==null?2e4:o.timeout),this._readyState="closed",this.uri=r;const u=o.parser||Ag;this.encoder=new u.Encoder,this.decoder=new u.Decoder,this._autoConnect=o.autoConnect!==!1,this._autoConnect&&this.open()}reconnection(r){return arguments.length?(this._reconnection=!!r,r||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(r){return r===void 0?this._reconnectionAttempts:(this._reconnectionAttempts=r,this)}reconnectionDelay(r){var o;return r===void 0?this._reconnectionDelay:(this._reconnectionDelay=r,(o=this.backoff)===null||o===void 0||o.setMin(r),this)}randomizationFactor(r){var o;return r===void 0?this._randomizationFactor:(this._randomizationFactor=r,(o=this.backoff)===null||o===void 0||o.setJitter(r),this)}reconnectionDelayMax(r){var o;return r===void 0?this._reconnectionDelayMax:(this._reconnectionDelayMax=r,(o=this.backoff)===null||o===void 0||o.setMax(r),this)}timeout(r){return arguments.length?(this._timeout=r,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&this.backoff.attempts===0&&this.reconnect()}open(r){if(~this._readyState.indexOf("open"))return this;this.engine=new kg(this.uri,this.opts);const o=this.engine,a=this;this._readyState="opening",this.skipReconnect=!1;const u=Dt(o,"open",function(){a.onopen(),r&&r()}),d=g=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",g),r?r(g):this.maybeReconnectOnOpen()},f=Dt(o,"error",d);if(this._timeout!==!1){const g=this._timeout,E=this.setTimeoutFn(()=>{u(),d(new Error("timeout")),o.close()},g);this.opts.autoUnref&&E.unref(),this.subs.push(()=>{this.clearTimeoutFn(E)})}return this.subs.push(u),this.subs.push(f),this}connect(r){return this.open(r)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");const r=this.engine;this.subs.push(Dt(r,"ping",this.onping.bind(this)),Dt(r,"data",this.ondata.bind(this)),Dt(r,"error",this.onerror.bind(this)),Dt(r,"close",this.onclose.bind(this)),Dt(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(r){try{this.decoder.add(r)}catch(o){this.onclose("parse error",o)}}ondecoded(r){os(()=>{this.emitReserved("packet",r)},this.setTimeoutFn)}onerror(r){this.emitReserved("error",r)}socket(r,o){let a=this.nsps[r];return a?this._autoConnect&&!a.active&&a.connect():(a=new Qd(this,r,o),this.nsps[r]=a),a}_destroy(r){const o=Object.keys(this.nsps);for(const a of o)if(this.nsps[a].active)return;this._close()}_packet(r){const o=this.encoder.encode(r);for(let a=0;a<o.length;a++)this.engine.write(o[a],r.options)}cleanup(){this.subs.forEach(r=>r()),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(r,o){var a;this.cleanup(),(a=this.engine)===null||a===void 0||a.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",r,o),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;const r=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{const o=this.backoff.duration();this._reconnecting=!0;const a=this.setTimeoutFn(()=>{r.skipReconnect||(this.emitReserved("reconnect_attempt",r.backoff.attempts),!r.skipReconnect&&r.open(u=>{u?(r._reconnecting=!1,r.reconnect(),this.emitReserved("reconnect_error",u)):r.onreconnect()}))},o);this.opts.autoUnref&&a.unref(),this.subs.push(()=>{this.clearTimeoutFn(a)})}}onreconnect(){const r=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",r)}}const xi={};function Wo(i,r){typeof i=="object"&&(r=i,i=void 0),r=r||{};const o=Sg(i,r.path||"/socket.io"),a=o.source,u=o.id,d=o.path,f=xi[u]&&d in xi[u].nsps,g=r.forceNew||r["force new connection"]||r.multiplex===!1||f;let E;return g?E=new xl(a,r):(xi[u]||(xi[u]=new xl(a,r)),E=xi[u]),o.query&&!r.query&&(r.query=o.queryKey),E.socket(o.path,r)}Object.assign(Wo,{Manager:xl,Socket:Qd,io:Wo,connect:Wo});var nl={exports:{}},rl,Kp;function zg(){if(Kp)return rl;Kp=1;var i="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";return rl=i,rl}var il,Qp;function Lg(){if(Qp)return il;Qp=1;var i=zg();function r(){}function o(){}return o.resetWarningCache=r,il=function(){function a(f,g,E,_,b,N){if(N!==i){var z=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw z.name="Invariant Violation",z}}a.isRequired=a;function u(){return a}var d={array:a,bigint:a,bool:a,func:a,number:a,object:a,string:a,symbol:a,any:a,arrayOf:u,element:a,elementType:a,instanceOf:u,node:a,objectOf:u,oneOf:u,oneOfType:u,shape:u,exact:u,checkPropTypes:o,resetWarningCache:r};return d.PropTypes=d,d},il}var Jp;function Fg(){return Jp||(Jp=1,nl.exports=Lg()()),nl.exports}var Ig=Fg();const we=Cl(Ig);function Yn(i,r,o,a){function u(d){return d instanceof o?d:new o(function(f){f(d)})}return new(o||(o=Promise))(function(d,f){function g(b){try{_(a.next(b))}catch(N){f(N)}}function E(b){try{_(a.throw(b))}catch(N){f(N)}}function _(b){b.done?d(b.value):u(b.value).then(g,E)}_((a=a.apply(i,r||[])).next())})}const Bg=new Map([["1km","application/vnd.1000minds.decision-model+xml"],["3dml","text/vnd.in3d.3dml"],["3ds","image/x-3ds"],["3g2","video/3gpp2"],["3gp","video/3gp"],["3gpp","video/3gpp"],["3mf","model/3mf"],["7z","application/x-7z-compressed"],["7zip","application/x-7z-compressed"],["123","application/vnd.lotus-1-2-3"],["aab","application/x-authorware-bin"],["aac","audio/x-acc"],["aam","application/x-authorware-map"],["aas","application/x-authorware-seg"],["abw","application/x-abiword"],["ac","application/vnd.nokia.n-gage.ac+xml"],["ac3","audio/ac3"],["acc","application/vnd.americandynamics.acc"],["ace","application/x-ace-compressed"],["acu","application/vnd.acucobol"],["acutc","application/vnd.acucorp"],["adp","audio/adpcm"],["aep","application/vnd.audiograph"],["afm","application/x-font-type1"],["afp","application/vnd.ibm.modcap"],["ahead","application/vnd.ahead.space"],["ai","application/pdf"],["aif","audio/x-aiff"],["aifc","audio/x-aiff"],["aiff","audio/x-aiff"],["air","application/vnd.adobe.air-application-installer-package+zip"],["ait","application/vnd.dvb.ait"],["ami","application/vnd.amiga.ami"],["amr","audio/amr"],["apk","application/vnd.android.package-archive"],["apng","image/apng"],["appcache","text/cache-manifest"],["application","application/x-ms-application"],["apr","application/vnd.lotus-approach"],["arc","application/x-freearc"],["arj","application/x-arj"],["asc","application/pgp-signature"],["asf","video/x-ms-asf"],["asm","text/x-asm"],["aso","application/vnd.accpac.simply.aso"],["asx","video/x-ms-asf"],["atc","application/vnd.acucorp"],["atom","application/atom+xml"],["atomcat","application/atomcat+xml"],["atomdeleted","application/atomdeleted+xml"],["atomsvc","application/atomsvc+xml"],["atx","application/vnd.antix.game-component"],["au","audio/x-au"],["avi","video/x-msvideo"],["avif","image/avif"],["aw","application/applixware"],["azf","application/vnd.airzip.filesecure.azf"],["azs","application/vnd.airzip.filesecure.azs"],["azv","image/vnd.airzip.accelerator.azv"],["azw","application/vnd.amazon.ebook"],["b16","image/vnd.pco.b16"],["bat","application/x-msdownload"],["bcpio","application/x-bcpio"],["bdf","application/x-font-bdf"],["bdm","application/vnd.syncml.dm+wbxml"],["bdoc","application/x-bdoc"],["bed","application/vnd.realvnc.bed"],["bh2","application/vnd.fujitsu.oasysprs"],["bin","application/octet-stream"],["blb","application/x-blorb"],["blorb","application/x-blorb"],["bmi","application/vnd.bmi"],["bmml","application/vnd.balsamiq.bmml+xml"],["bmp","image/bmp"],["book","application/vnd.framemaker"],["box","application/vnd.previewsystems.box"],["boz","application/x-bzip2"],["bpk","application/octet-stream"],["bpmn","application/octet-stream"],["bsp","model/vnd.valve.source.compiled-map"],["btif","image/prs.btif"],["buffer","application/octet-stream"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["c","text/x-c"],["c4d","application/vnd.clonk.c4group"],["c4f","application/vnd.clonk.c4group"],["c4g","application/vnd.clonk.c4group"],["c4p","application/vnd.clonk.c4group"],["c4u","application/vnd.clonk.c4group"],["c11amc","application/vnd.cluetrust.cartomobile-config"],["c11amz","application/vnd.cluetrust.cartomobile-config-pkg"],["cab","application/vnd.ms-cab-compressed"],["caf","audio/x-caf"],["cap","application/vnd.tcpdump.pcap"],["car","application/vnd.curl.car"],["cat","application/vnd.ms-pki.seccat"],["cb7","application/x-cbr"],["cba","application/x-cbr"],["cbr","application/x-cbr"],["cbt","application/x-cbr"],["cbz","application/x-cbr"],["cc","text/x-c"],["cco","application/x-cocoa"],["cct","application/x-director"],["ccxml","application/ccxml+xml"],["cdbcmsg","application/vnd.contact.cmsg"],["cda","application/x-cdf"],["cdf","application/x-netcdf"],["cdfx","application/cdfx+xml"],["cdkey","application/vnd.mediastation.cdkey"],["cdmia","application/cdmi-capability"],["cdmic","application/cdmi-container"],["cdmid","application/cdmi-domain"],["cdmio","application/cdmi-object"],["cdmiq","application/cdmi-queue"],["cdr","application/cdr"],["cdx","chemical/x-cdx"],["cdxml","application/vnd.chemdraw+xml"],["cdy","application/vnd.cinderella"],["cer","application/pkix-cert"],["cfs","application/x-cfs-compressed"],["cgm","image/cgm"],["chat","application/x-chat"],["chm","application/vnd.ms-htmlhelp"],["chrt","application/vnd.kde.kchart"],["cif","chemical/x-cif"],["cii","application/vnd.anser-web-certificate-issue-initiation"],["cil","application/vnd.ms-artgalry"],["cjs","application/node"],["cla","application/vnd.claymore"],["class","application/octet-stream"],["clkk","application/vnd.crick.clicker.keyboard"],["clkp","application/vnd.crick.clicker.palette"],["clkt","application/vnd.crick.clicker.template"],["clkw","application/vnd.crick.clicker.wordbank"],["clkx","application/vnd.crick.clicker"],["clp","application/x-msclip"],["cmc","application/vnd.cosmocaller"],["cmdf","chemical/x-cmdf"],["cml","chemical/x-cml"],["cmp","application/vnd.yellowriver-custom-menu"],["cmx","image/x-cmx"],["cod","application/vnd.rim.cod"],["coffee","text/coffeescript"],["com","application/x-msdownload"],["conf","text/plain"],["cpio","application/x-cpio"],["cpp","text/x-c"],["cpt","application/mac-compactpro"],["crd","application/x-mscardfile"],["crl","application/pkix-crl"],["crt","application/x-x509-ca-cert"],["crx","application/x-chrome-extension"],["cryptonote","application/vnd.rig.cryptonote"],["csh","application/x-csh"],["csl","application/vnd.citationstyles.style+xml"],["csml","chemical/x-csml"],["csp","application/vnd.commonspace"],["csr","application/octet-stream"],["css","text/css"],["cst","application/x-director"],["csv","text/csv"],["cu","application/cu-seeme"],["curl","text/vnd.curl"],["cww","application/prs.cww"],["cxt","application/x-director"],["cxx","text/x-c"],["dae","model/vnd.collada+xml"],["daf","application/vnd.mobius.daf"],["dart","application/vnd.dart"],["dataless","application/vnd.fdsn.seed"],["davmount","application/davmount+xml"],["dbf","application/vnd.dbf"],["dbk","application/docbook+xml"],["dcr","application/x-director"],["dcurl","text/vnd.curl.dcurl"],["dd2","application/vnd.oma.dd2+xml"],["ddd","application/vnd.fujixerox.ddd"],["ddf","application/vnd.syncml.dmddf+xml"],["dds","image/vnd.ms-dds"],["deb","application/x-debian-package"],["def","text/plain"],["deploy","application/octet-stream"],["der","application/x-x509-ca-cert"],["dfac","application/vnd.dreamfactory"],["dgc","application/x-dgc-compressed"],["dic","text/x-c"],["dir","application/x-director"],["dis","application/vnd.mobius.dis"],["disposition-notification","message/disposition-notification"],["dist","application/octet-stream"],["distz","application/octet-stream"],["djv","image/vnd.djvu"],["djvu","image/vnd.djvu"],["dll","application/octet-stream"],["dmg","application/x-apple-diskimage"],["dmn","application/octet-stream"],["dmp","application/vnd.tcpdump.pcap"],["dms","application/octet-stream"],["dna","application/vnd.dna"],["doc","application/msword"],["docm","application/vnd.ms-word.template.macroEnabled.12"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["dot","application/msword"],["dotm","application/vnd.ms-word.template.macroEnabled.12"],["dotx","application/vnd.openxmlformats-officedocument.wordprocessingml.template"],["dp","application/vnd.osgi.dp"],["dpg","application/vnd.dpgraph"],["dra","audio/vnd.dra"],["drle","image/dicom-rle"],["dsc","text/prs.lines.tag"],["dssc","application/dssc+der"],["dtb","application/x-dtbook+xml"],["dtd","application/xml-dtd"],["dts","audio/vnd.dts"],["dtshd","audio/vnd.dts.hd"],["dump","application/octet-stream"],["dvb","video/vnd.dvb.file"],["dvi","application/x-dvi"],["dwd","application/atsc-dwd+xml"],["dwf","model/vnd.dwf"],["dwg","image/vnd.dwg"],["dxf","image/vnd.dxf"],["dxp","application/vnd.spotfire.dxp"],["dxr","application/x-director"],["ear","application/java-archive"],["ecelp4800","audio/vnd.nuera.ecelp4800"],["ecelp7470","audio/vnd.nuera.ecelp7470"],["ecelp9600","audio/vnd.nuera.ecelp9600"],["ecma","application/ecmascript"],["edm","application/vnd.novadigm.edm"],["edx","application/vnd.novadigm.edx"],["efif","application/vnd.picsel"],["ei6","application/vnd.pg.osasli"],["elc","application/octet-stream"],["emf","image/emf"],["eml","message/rfc822"],["emma","application/emma+xml"],["emotionml","application/emotionml+xml"],["emz","application/x-msmetafile"],["eol","audio/vnd.digital-winds"],["eot","application/vnd.ms-fontobject"],["eps","application/postscript"],["epub","application/epub+zip"],["es","application/ecmascript"],["es3","application/vnd.eszigno3+xml"],["esa","application/vnd.osgi.subsystem"],["esf","application/vnd.epson.esf"],["et3","application/vnd.eszigno3+xml"],["etx","text/x-setext"],["eva","application/x-eva"],["evy","application/x-envoy"],["exe","application/octet-stream"],["exi","application/exi"],["exp","application/express"],["exr","image/aces"],["ext","application/vnd.novadigm.ext"],["ez","application/andrew-inset"],["ez2","application/vnd.ezpix-album"],["ez3","application/vnd.ezpix-package"],["f","text/x-fortran"],["f4v","video/mp4"],["f77","text/x-fortran"],["f90","text/x-fortran"],["fbs","image/vnd.fastbidsheet"],["fcdt","application/vnd.adobe.formscentral.fcdt"],["fcs","application/vnd.isac.fcs"],["fdf","application/vnd.fdf"],["fdt","application/fdt+xml"],["fe_launch","application/vnd.denovo.fcselayout-link"],["fg5","application/vnd.fujitsu.oasysgp"],["fgd","application/x-director"],["fh","image/x-freehand"],["fh4","image/x-freehand"],["fh5","image/x-freehand"],["fh7","image/x-freehand"],["fhc","image/x-freehand"],["fig","application/x-xfig"],["fits","image/fits"],["flac","audio/x-flac"],["fli","video/x-fli"],["flo","application/vnd.micrografx.flo"],["flv","video/x-flv"],["flw","application/vnd.kde.kivio"],["flx","text/vnd.fmi.flexstor"],["fly","text/vnd.fly"],["fm","application/vnd.framemaker"],["fnc","application/vnd.frogans.fnc"],["fo","application/vnd.software602.filler.form+xml"],["for","text/x-fortran"],["fpx","image/vnd.fpx"],["frame","application/vnd.framemaker"],["fsc","application/vnd.fsc.weblaunch"],["fst","image/vnd.fst"],["ftc","application/vnd.fluxtime.clip"],["fti","application/vnd.anser-web-funds-transfer-initiation"],["fvt","video/vnd.fvt"],["fxp","application/vnd.adobe.fxp"],["fxpl","application/vnd.adobe.fxp"],["fzs","application/vnd.fuzzysheet"],["g2w","application/vnd.geoplan"],["g3","image/g3fax"],["g3w","application/vnd.geospace"],["gac","application/vnd.groove-account"],["gam","application/x-tads"],["gbr","application/rpki-ghostbusters"],["gca","application/x-gca-compressed"],["gdl","model/vnd.gdl"],["gdoc","application/vnd.google-apps.document"],["geo","application/vnd.dynageo"],["geojson","application/geo+json"],["gex","application/vnd.geometry-explorer"],["ggb","application/vnd.geogebra.file"],["ggt","application/vnd.geogebra.tool"],["ghf","application/vnd.groove-help"],["gif","image/gif"],["gim","application/vnd.groove-identity-message"],["glb","model/gltf-binary"],["gltf","model/gltf+json"],["gml","application/gml+xml"],["gmx","application/vnd.gmx"],["gnumeric","application/x-gnumeric"],["gpg","application/gpg-keys"],["gph","application/vnd.flographit"],["gpx","application/gpx+xml"],["gqf","application/vnd.grafeq"],["gqs","application/vnd.grafeq"],["gram","application/srgs"],["gramps","application/x-gramps-xml"],["gre","application/vnd.geometry-explorer"],["grv","application/vnd.groove-injector"],["grxml","application/srgs+xml"],["gsf","application/x-font-ghostscript"],["gsheet","application/vnd.google-apps.spreadsheet"],["gslides","application/vnd.google-apps.presentation"],["gtar","application/x-gtar"],["gtm","application/vnd.groove-tool-message"],["gtw","model/vnd.gtw"],["gv","text/vnd.graphviz"],["gxf","application/gxf"],["gxt","application/vnd.geonext"],["gz","application/gzip"],["gzip","application/gzip"],["h","text/x-c"],["h261","video/h261"],["h263","video/h263"],["h264","video/h264"],["hal","application/vnd.hal+xml"],["hbci","application/vnd.hbci"],["hbs","text/x-handlebars-template"],["hdd","application/x-virtualbox-hdd"],["hdf","application/x-hdf"],["heic","image/heic"],["heics","image/heic-sequence"],["heif","image/heif"],["heifs","image/heif-sequence"],["hej2","image/hej2k"],["held","application/atsc-held+xml"],["hh","text/x-c"],["hjson","application/hjson"],["hlp","application/winhlp"],["hpgl","application/vnd.hp-hpgl"],["hpid","application/vnd.hp-hpid"],["hps","application/vnd.hp-hps"],["hqx","application/mac-binhex40"],["hsj2","image/hsj2"],["htc","text/x-component"],["htke","application/vnd.kenameaapp"],["htm","text/html"],["html","text/html"],["hvd","application/vnd.yamaha.hv-dic"],["hvp","application/vnd.yamaha.hv-voice"],["hvs","application/vnd.yamaha.hv-script"],["i2g","application/vnd.intergeo"],["icc","application/vnd.iccprofile"],["ice","x-conference/x-cooltalk"],["icm","application/vnd.iccprofile"],["ico","image/x-icon"],["ics","text/calendar"],["ief","image/ief"],["ifb","text/calendar"],["ifm","application/vnd.shana.informed.formdata"],["iges","model/iges"],["igl","application/vnd.igloader"],["igm","application/vnd.insors.igm"],["igs","model/iges"],["igx","application/vnd.micrografx.igx"],["iif","application/vnd.shana.informed.interchange"],["img","application/octet-stream"],["imp","application/vnd.accpac.simply.imp"],["ims","application/vnd.ms-ims"],["in","text/plain"],["ini","text/plain"],["ink","application/inkml+xml"],["inkml","application/inkml+xml"],["install","application/x-install-instructions"],["iota","application/vnd.astraea-software.iota"],["ipfix","application/ipfix"],["ipk","application/vnd.shana.informed.package"],["irm","application/vnd.ibm.rights-management"],["irp","application/vnd.irepository.package+xml"],["iso","application/x-iso9660-image"],["itp","application/vnd.shana.informed.formtemplate"],["its","application/its+xml"],["ivp","application/vnd.immervision-ivp"],["ivu","application/vnd.immervision-ivu"],["jad","text/vnd.sun.j2me.app-descriptor"],["jade","text/jade"],["jam","application/vnd.jam"],["jar","application/java-archive"],["jardiff","application/x-java-archive-diff"],["java","text/x-java-source"],["jhc","image/jphc"],["jisp","application/vnd.jisp"],["jls","image/jls"],["jlt","application/vnd.hp-jlyt"],["jng","image/x-jng"],["jnlp","application/x-java-jnlp-file"],["joda","application/vnd.joost.joda-archive"],["jp2","image/jp2"],["jpe","image/jpeg"],["jpeg","image/jpeg"],["jpf","image/jpx"],["jpg","image/jpeg"],["jpg2","image/jp2"],["jpgm","video/jpm"],["jpgv","video/jpeg"],["jph","image/jph"],["jpm","video/jpm"],["jpx","image/jpx"],["js","application/javascript"],["json","application/json"],["json5","application/json5"],["jsonld","application/ld+json"],["jsonl","application/jsonl"],["jsonml","application/jsonml+json"],["jsx","text/jsx"],["jxr","image/jxr"],["jxra","image/jxra"],["jxrs","image/jxrs"],["jxs","image/jxs"],["jxsc","image/jxsc"],["jxsi","image/jxsi"],["jxss","image/jxss"],["kar","audio/midi"],["karbon","application/vnd.kde.karbon"],["kdb","application/octet-stream"],["kdbx","application/x-keepass2"],["key","application/x-iwork-keynote-sffkey"],["kfo","application/vnd.kde.kformula"],["kia","application/vnd.kidspiration"],["kml","application/vnd.google-earth.kml+xml"],["kmz","application/vnd.google-earth.kmz"],["kne","application/vnd.kinar"],["knp","application/vnd.kinar"],["kon","application/vnd.kde.kontour"],["kpr","application/vnd.kde.kpresenter"],["kpt","application/vnd.kde.kpresenter"],["kpxx","application/vnd.ds-keypoint"],["ksp","application/vnd.kde.kspread"],["ktr","application/vnd.kahootz"],["ktx","image/ktx"],["ktx2","image/ktx2"],["ktz","application/vnd.kahootz"],["kwd","application/vnd.kde.kword"],["kwt","application/vnd.kde.kword"],["lasxml","application/vnd.las.las+xml"],["latex","application/x-latex"],["lbd","application/vnd.llamagraphics.life-balance.desktop"],["lbe","application/vnd.llamagraphics.life-balance.exchange+xml"],["les","application/vnd.hhe.lesson-player"],["less","text/less"],["lgr","application/lgr+xml"],["lha","application/octet-stream"],["link66","application/vnd.route66.link66+xml"],["list","text/plain"],["list3820","application/vnd.ibm.modcap"],["listafp","application/vnd.ibm.modcap"],["litcoffee","text/coffeescript"],["lnk","application/x-ms-shortcut"],["log","text/plain"],["lostxml","application/lost+xml"],["lrf","application/octet-stream"],["lrm","application/vnd.ms-lrm"],["ltf","application/vnd.frogans.ltf"],["lua","text/x-lua"],["luac","application/x-lua-bytecode"],["lvp","audio/vnd.lucent.voice"],["lwp","application/vnd.lotus-wordpro"],["lzh","application/octet-stream"],["m1v","video/mpeg"],["m2a","audio/mpeg"],["m2v","video/mpeg"],["m3a","audio/mpeg"],["m3u","text/plain"],["m3u8","application/vnd.apple.mpegurl"],["m4a","audio/x-m4a"],["m4p","application/mp4"],["m4s","video/iso.segment"],["m4u","application/vnd.mpegurl"],["m4v","video/x-m4v"],["m13","application/x-msmediaview"],["m14","application/x-msmediaview"],["m21","application/mp21"],["ma","application/mathematica"],["mads","application/mads+xml"],["maei","application/mmt-aei+xml"],["mag","application/vnd.ecowin.chart"],["maker","application/vnd.framemaker"],["man","text/troff"],["manifest","text/cache-manifest"],["map","application/json"],["mar","application/octet-stream"],["markdown","text/markdown"],["mathml","application/mathml+xml"],["mb","application/mathematica"],["mbk","application/vnd.mobius.mbk"],["mbox","application/mbox"],["mc1","application/vnd.medcalcdata"],["mcd","application/vnd.mcd"],["mcurl","text/vnd.curl.mcurl"],["md","text/markdown"],["mdb","application/x-msaccess"],["mdi","image/vnd.ms-modi"],["mdx","text/mdx"],["me","text/troff"],["mesh","model/mesh"],["meta4","application/metalink4+xml"],["metalink","application/metalink+xml"],["mets","application/mets+xml"],["mfm","application/vnd.mfmp"],["mft","application/rpki-manifest"],["mgp","application/vnd.osgeo.mapguide.package"],["mgz","application/vnd.proteus.magazine"],["mid","audio/midi"],["midi","audio/midi"],["mie","application/x-mie"],["mif","application/vnd.mif"],["mime","message/rfc822"],["mj2","video/mj2"],["mjp2","video/mj2"],["mjs","application/javascript"],["mk3d","video/x-matroska"],["mka","audio/x-matroska"],["mkd","text/x-markdown"],["mks","video/x-matroska"],["mkv","video/x-matroska"],["mlp","application/vnd.dolby.mlp"],["mmd","application/vnd.chipnuts.karaoke-mmd"],["mmf","application/vnd.smaf"],["mml","text/mathml"],["mmr","image/vnd.fujixerox.edmics-mmr"],["mng","video/x-mng"],["mny","application/x-msmoney"],["mobi","application/x-mobipocket-ebook"],["mods","application/mods+xml"],["mov","video/quicktime"],["movie","video/x-sgi-movie"],["mp2","audio/mpeg"],["mp2a","audio/mpeg"],["mp3","audio/mpeg"],["mp4","video/mp4"],["mp4a","audio/mp4"],["mp4s","application/mp4"],["mp4v","video/mp4"],["mp21","application/mp21"],["mpc","application/vnd.mophun.certificate"],["mpd","application/dash+xml"],["mpe","video/mpeg"],["mpeg","video/mpeg"],["mpg","video/mpeg"],["mpg4","video/mp4"],["mpga","audio/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["mpm","application/vnd.blueice.multipass"],["mpn","application/vnd.mophun.application"],["mpp","application/vnd.ms-project"],["mpt","application/vnd.ms-project"],["mpy","application/vnd.ibm.minipay"],["mqy","application/vnd.mobius.mqy"],["mrc","application/marc"],["mrcx","application/marcxml+xml"],["ms","text/troff"],["mscml","application/mediaservercontrol+xml"],["mseed","application/vnd.fdsn.mseed"],["mseq","application/vnd.mseq"],["msf","application/vnd.epson.msf"],["msg","application/vnd.ms-outlook"],["msh","model/mesh"],["msi","application/x-msdownload"],["msl","application/vnd.mobius.msl"],["msm","application/octet-stream"],["msp","application/octet-stream"],["msty","application/vnd.muvee.style"],["mtl","model/mtl"],["mts","model/vnd.mts"],["mus","application/vnd.musician"],["musd","application/mmt-usd+xml"],["musicxml","application/vnd.recordare.musicxml+xml"],["mvb","application/x-msmediaview"],["mvt","application/vnd.mapbox-vector-tile"],["mwf","application/vnd.mfer"],["mxf","application/mxf"],["mxl","application/vnd.recordare.musicxml"],["mxmf","audio/mobile-xmf"],["mxml","application/xv+xml"],["mxs","application/vnd.triscape.mxs"],["mxu","video/vnd.mpegurl"],["n-gage","application/vnd.nokia.n-gage.symbian.install"],["n3","text/n3"],["nb","application/mathematica"],["nbp","application/vnd.wolfram.player"],["nc","application/x-netcdf"],["ncx","application/x-dtbncx+xml"],["nfo","text/x-nfo"],["ngdat","application/vnd.nokia.n-gage.data"],["nitf","application/vnd.nitf"],["nlu","application/vnd.neurolanguage.nlu"],["nml","application/vnd.enliven"],["nnd","application/vnd.noblenet-directory"],["nns","application/vnd.noblenet-sealer"],["nnw","application/vnd.noblenet-web"],["npx","image/vnd.net-fpx"],["nq","application/n-quads"],["nsc","application/x-conference"],["nsf","application/vnd.lotus-notes"],["nt","application/n-triples"],["ntf","application/vnd.nitf"],["numbers","application/x-iwork-numbers-sffnumbers"],["nzb","application/x-nzb"],["oa2","application/vnd.fujitsu.oasys2"],["oa3","application/vnd.fujitsu.oasys3"],["oas","application/vnd.fujitsu.oasys"],["obd","application/x-msbinder"],["obgx","application/vnd.openblox.game+xml"],["obj","model/obj"],["oda","application/oda"],["odb","application/vnd.oasis.opendocument.database"],["odc","application/vnd.oasis.opendocument.chart"],["odf","application/vnd.oasis.opendocument.formula"],["odft","application/vnd.oasis.opendocument.formula-template"],["odg","application/vnd.oasis.opendocument.graphics"],["odi","application/vnd.oasis.opendocument.image"],["odm","application/vnd.oasis.opendocument.text-master"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogex","model/vnd.opengex"],["ogg","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["omdoc","application/omdoc+xml"],["onepkg","application/onenote"],["onetmp","application/onenote"],["onetoc","application/onenote"],["onetoc2","application/onenote"],["opf","application/oebps-package+xml"],["opml","text/x-opml"],["oprc","application/vnd.palm"],["opus","audio/ogg"],["org","text/x-org"],["osf","application/vnd.yamaha.openscoreformat"],["osfpvg","application/vnd.yamaha.openscoreformat.osfpvg+xml"],["osm","application/vnd.openstreetmap.data+xml"],["otc","application/vnd.oasis.opendocument.chart-template"],["otf","font/otf"],["otg","application/vnd.oasis.opendocument.graphics-template"],["oth","application/vnd.oasis.opendocument.text-web"],["oti","application/vnd.oasis.opendocument.image-template"],["otp","application/vnd.oasis.opendocument.presentation-template"],["ots","application/vnd.oasis.opendocument.spreadsheet-template"],["ott","application/vnd.oasis.opendocument.text-template"],["ova","application/x-virtualbox-ova"],["ovf","application/x-virtualbox-ovf"],["owl","application/rdf+xml"],["oxps","application/oxps"],["oxt","application/vnd.openofficeorg.extension"],["p","text/x-pascal"],["p7a","application/x-pkcs7-signature"],["p7b","application/x-pkcs7-certificates"],["p7c","application/pkcs7-mime"],["p7m","application/pkcs7-mime"],["p7r","application/x-pkcs7-certreqresp"],["p7s","application/pkcs7-signature"],["p8","application/pkcs8"],["p10","application/x-pkcs10"],["p12","application/x-pkcs12"],["pac","application/x-ns-proxy-autoconfig"],["pages","application/x-iwork-pages-sffpages"],["pas","text/x-pascal"],["paw","application/vnd.pawaafile"],["pbd","application/vnd.powerbuilder6"],["pbm","image/x-portable-bitmap"],["pcap","application/vnd.tcpdump.pcap"],["pcf","application/x-font-pcf"],["pcl","application/vnd.hp-pcl"],["pclxl","application/vnd.hp-pclxl"],["pct","image/x-pict"],["pcurl","application/vnd.curl.pcurl"],["pcx","image/x-pcx"],["pdb","application/x-pilot"],["pde","text/x-processing"],["pdf","application/pdf"],["pem","application/x-x509-user-cert"],["pfa","application/x-font-type1"],["pfb","application/x-font-type1"],["pfm","application/x-font-type1"],["pfr","application/font-tdpfr"],["pfx","application/x-pkcs12"],["pgm","image/x-portable-graymap"],["pgn","application/x-chess-pgn"],["pgp","application/pgp"],["php","application/x-httpd-php"],["php3","application/x-httpd-php"],["php4","application/x-httpd-php"],["phps","application/x-httpd-php-source"],["phtml","application/x-httpd-php"],["pic","image/x-pict"],["pkg","application/octet-stream"],["pki","application/pkixcmp"],["pkipath","application/pkix-pkipath"],["pkpass","application/vnd.apple.pkpass"],["pl","application/x-perl"],["plb","application/vnd.3gpp.pic-bw-large"],["plc","application/vnd.mobius.plc"],["plf","application/vnd.pocketlearn"],["pls","application/pls+xml"],["pm","application/x-perl"],["pml","application/vnd.ctc-posml"],["png","image/png"],["pnm","image/x-portable-anymap"],["portpkg","application/vnd.macports.portpkg"],["pot","application/vnd.ms-powerpoint"],["potm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["potx","application/vnd.openxmlformats-officedocument.presentationml.template"],["ppa","application/vnd.ms-powerpoint"],["ppam","application/vnd.ms-powerpoint.addin.macroEnabled.12"],["ppd","application/vnd.cups-ppd"],["ppm","image/x-portable-pixmap"],["pps","application/vnd.ms-powerpoint"],["ppsm","application/vnd.ms-powerpoint.slideshow.macroEnabled.12"],["ppsx","application/vnd.openxmlformats-officedocument.presentationml.slideshow"],["ppt","application/powerpoint"],["pptm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["pqa","application/vnd.palm"],["prc","application/x-pilot"],["pre","application/vnd.lotus-freelance"],["prf","application/pics-rules"],["provx","application/provenance+xml"],["ps","application/postscript"],["psb","application/vnd.3gpp.pic-bw-small"],["psd","application/x-photoshop"],["psf","application/x-font-linux-psf"],["pskcxml","application/pskc+xml"],["pti","image/prs.pti"],["ptid","application/vnd.pvi.ptid1"],["pub","application/x-mspublisher"],["pvb","application/vnd.3gpp.pic-bw-var"],["pwn","application/vnd.3m.post-it-notes"],["pya","audio/vnd.ms-playready.media.pya"],["pyv","video/vnd.ms-playready.media.pyv"],["qam","application/vnd.epson.quickanime"],["qbo","application/vnd.intu.qbo"],["qfx","application/vnd.intu.qfx"],["qps","application/vnd.publishare-delta-tree"],["qt","video/quicktime"],["qwd","application/vnd.quark.quarkxpress"],["qwt","application/vnd.quark.quarkxpress"],["qxb","application/vnd.quark.quarkxpress"],["qxd","application/vnd.quark.quarkxpress"],["qxl","application/vnd.quark.quarkxpress"],["qxt","application/vnd.quark.quarkxpress"],["ra","audio/x-realaudio"],["ram","audio/x-pn-realaudio"],["raml","application/raml+yaml"],["rapd","application/route-apd+xml"],["rar","application/x-rar"],["ras","image/x-cmu-raster"],["rcprofile","application/vnd.ipunplugged.rcprofile"],["rdf","application/rdf+xml"],["rdz","application/vnd.data-vision.rdz"],["relo","application/p2p-overlay+xml"],["rep","application/vnd.businessobjects"],["res","application/x-dtbresource+xml"],["rgb","image/x-rgb"],["rif","application/reginfo+xml"],["rip","audio/vnd.rip"],["ris","application/x-research-info-systems"],["rl","application/resource-lists+xml"],["rlc","image/vnd.fujixerox.edmics-rlc"],["rld","application/resource-lists-diff+xml"],["rm","audio/x-pn-realaudio"],["rmi","audio/midi"],["rmp","audio/x-pn-realaudio-plugin"],["rms","application/vnd.jcp.javame.midlet-rms"],["rmvb","application/vnd.rn-realmedia-vbr"],["rnc","application/relax-ng-compact-syntax"],["rng","application/xml"],["roa","application/rpki-roa"],["roff","text/troff"],["rp9","application/vnd.cloanto.rp9"],["rpm","audio/x-pn-realaudio-plugin"],["rpss","application/vnd.nokia.radio-presets"],["rpst","application/vnd.nokia.radio-preset"],["rq","application/sparql-query"],["rs","application/rls-services+xml"],["rsa","application/x-pkcs7"],["rsat","application/atsc-rsat+xml"],["rsd","application/rsd+xml"],["rsheet","application/urc-ressheet+xml"],["rss","application/rss+xml"],["rtf","text/rtf"],["rtx","text/richtext"],["run","application/x-makeself"],["rusd","application/route-usd+xml"],["rv","video/vnd.rn-realvideo"],["s","text/x-asm"],["s3m","audio/s3m"],["saf","application/vnd.yamaha.smaf-audio"],["sass","text/x-sass"],["sbml","application/sbml+xml"],["sc","application/vnd.ibm.secure-container"],["scd","application/x-msschedule"],["scm","application/vnd.lotus-screencam"],["scq","application/scvp-cv-request"],["scs","application/scvp-cv-response"],["scss","text/x-scss"],["scurl","text/vnd.curl.scurl"],["sda","application/vnd.stardivision.draw"],["sdc","application/vnd.stardivision.calc"],["sdd","application/vnd.stardivision.impress"],["sdkd","application/vnd.solent.sdkm+xml"],["sdkm","application/vnd.solent.sdkm+xml"],["sdp","application/sdp"],["sdw","application/vnd.stardivision.writer"],["sea","application/octet-stream"],["see","application/vnd.seemail"],["seed","application/vnd.fdsn.seed"],["sema","application/vnd.sema"],["semd","application/vnd.semd"],["semf","application/vnd.semf"],["senmlx","application/senml+xml"],["sensmlx","application/sensml+xml"],["ser","application/java-serialized-object"],["setpay","application/set-payment-initiation"],["setreg","application/set-registration-initiation"],["sfd-hdstx","application/vnd.hydrostatix.sof-data"],["sfs","application/vnd.spotfire.sfs"],["sfv","text/x-sfv"],["sgi","image/sgi"],["sgl","application/vnd.stardivision.writer-global"],["sgm","text/sgml"],["sgml","text/sgml"],["sh","application/x-sh"],["shar","application/x-shar"],["shex","text/shex"],["shf","application/shf+xml"],["shtml","text/html"],["sid","image/x-mrsid-image"],["sieve","application/sieve"],["sig","application/pgp-signature"],["sil","audio/silk"],["silo","model/mesh"],["sis","application/vnd.symbian.install"],["sisx","application/vnd.symbian.install"],["sit","application/x-stuffit"],["sitx","application/x-stuffitx"],["siv","application/sieve"],["skd","application/vnd.koan"],["skm","application/vnd.koan"],["skp","application/vnd.koan"],["skt","application/vnd.koan"],["sldm","application/vnd.ms-powerpoint.slide.macroenabled.12"],["sldx","application/vnd.openxmlformats-officedocument.presentationml.slide"],["slim","text/slim"],["slm","text/slim"],["sls","application/route-s-tsid+xml"],["slt","application/vnd.epson.salt"],["sm","application/vnd.stepmania.stepchart"],["smf","application/vnd.stardivision.math"],["smi","application/smil"],["smil","application/smil"],["smv","video/x-smv"],["smzip","application/vnd.stepmania.package"],["snd","audio/basic"],["snf","application/x-font-snf"],["so","application/octet-stream"],["spc","application/x-pkcs7-certificates"],["spdx","text/spdx"],["spf","application/vnd.yamaha.smaf-phrase"],["spl","application/x-futuresplash"],["spot","text/vnd.in3d.spot"],["spp","application/scvp-vp-response"],["spq","application/scvp-vp-request"],["spx","audio/ogg"],["sql","application/x-sql"],["src","application/x-wais-source"],["srt","application/x-subrip"],["sru","application/sru+xml"],["srx","application/sparql-results+xml"],["ssdl","application/ssdl+xml"],["sse","application/vnd.kodak-descriptor"],["ssf","application/vnd.epson.ssf"],["ssml","application/ssml+xml"],["sst","application/octet-stream"],["st","application/vnd.sailingtracker.track"],["stc","application/vnd.sun.xml.calc.template"],["std","application/vnd.sun.xml.draw.template"],["stf","application/vnd.wt.stf"],["sti","application/vnd.sun.xml.impress.template"],["stk","application/hyperstudio"],["stl","model/stl"],["stpx","model/step+xml"],["stpxz","model/step-xml+zip"],["stpz","model/step+zip"],["str","application/vnd.pg.format"],["stw","application/vnd.sun.xml.writer.template"],["styl","text/stylus"],["stylus","text/stylus"],["sub","text/vnd.dvb.subtitle"],["sus","application/vnd.sus-calendar"],["susp","application/vnd.sus-calendar"],["sv4cpio","application/x-sv4cpio"],["sv4crc","application/x-sv4crc"],["svc","application/vnd.dvb.service"],["svd","application/vnd.svd"],["svg","image/svg+xml"],["svgz","image/svg+xml"],["swa","application/x-director"],["swf","application/x-shockwave-flash"],["swi","application/vnd.aristanetworks.swi"],["swidtag","application/swid+xml"],["sxc","application/vnd.sun.xml.calc"],["sxd","application/vnd.sun.xml.draw"],["sxg","application/vnd.sun.xml.writer.global"],["sxi","application/vnd.sun.xml.impress"],["sxm","application/vnd.sun.xml.math"],["sxw","application/vnd.sun.xml.writer"],["t","text/troff"],["t3","application/x-t3vm-image"],["t38","image/t38"],["taglet","application/vnd.mynfc"],["tao","application/vnd.tao.intent-module-archive"],["tap","image/vnd.tencent.tap"],["tar","application/x-tar"],["tcap","application/vnd.3gpp2.tcap"],["tcl","application/x-tcl"],["td","application/urc-targetdesc+xml"],["teacher","application/vnd.smart.teacher"],["tei","application/tei+xml"],["teicorpus","application/tei+xml"],["tex","application/x-tex"],["texi","application/x-texinfo"],["texinfo","application/x-texinfo"],["text","text/plain"],["tfi","application/thraud+xml"],["tfm","application/x-tex-tfm"],["tfx","image/tiff-fx"],["tga","image/x-tga"],["tgz","application/x-tar"],["thmx","application/vnd.ms-officetheme"],["tif","image/tiff"],["tiff","image/tiff"],["tk","application/x-tcl"],["tmo","application/vnd.tmobile-livetv"],["toml","application/toml"],["torrent","application/x-bittorrent"],["tpl","application/vnd.groove-tool-template"],["tpt","application/vnd.trid.tpt"],["tr","text/troff"],["tra","application/vnd.trueapp"],["trig","application/trig"],["trm","application/x-msterminal"],["ts","video/mp2t"],["tsd","application/timestamped-data"],["tsv","text/tab-separated-values"],["ttc","font/collection"],["ttf","font/ttf"],["ttl","text/turtle"],["ttml","application/ttml+xml"],["twd","application/vnd.simtech-mindmapper"],["twds","application/vnd.simtech-mindmapper"],["txd","application/vnd.genomatix.tuxedo"],["txf","application/vnd.mobius.txf"],["txt","text/plain"],["u8dsn","message/global-delivery-status"],["u8hdr","message/global-headers"],["u8mdn","message/global-disposition-notification"],["u8msg","message/global"],["u32","application/x-authorware-bin"],["ubj","application/ubjson"],["udeb","application/x-debian-package"],["ufd","application/vnd.ufdl"],["ufdl","application/vnd.ufdl"],["ulx","application/x-glulx"],["umj","application/vnd.umajin"],["unityweb","application/vnd.unity"],["uoml","application/vnd.uoml+xml"],["uri","text/uri-list"],["uris","text/uri-list"],["urls","text/uri-list"],["usdz","model/vnd.usdz+zip"],["ustar","application/x-ustar"],["utz","application/vnd.uiq.theme"],["uu","text/x-uuencode"],["uva","audio/vnd.dece.audio"],["uvd","application/vnd.dece.data"],["uvf","application/vnd.dece.data"],["uvg","image/vnd.dece.graphic"],["uvh","video/vnd.dece.hd"],["uvi","image/vnd.dece.graphic"],["uvm","video/vnd.dece.mobile"],["uvp","video/vnd.dece.pd"],["uvs","video/vnd.dece.sd"],["uvt","application/vnd.dece.ttml+xml"],["uvu","video/vnd.uvvu.mp4"],["uvv","video/vnd.dece.video"],["uvva","audio/vnd.dece.audio"],["uvvd","application/vnd.dece.data"],["uvvf","application/vnd.dece.data"],["uvvg","image/vnd.dece.graphic"],["uvvh","video/vnd.dece.hd"],["uvvi","image/vnd.dece.graphic"],["uvvm","video/vnd.dece.mobile"],["uvvp","video/vnd.dece.pd"],["uvvs","video/vnd.dece.sd"],["uvvt","application/vnd.dece.ttml+xml"],["uvvu","video/vnd.uvvu.mp4"],["uvvv","video/vnd.dece.video"],["uvvx","application/vnd.dece.unspecified"],["uvvz","application/vnd.dece.zip"],["uvx","application/vnd.dece.unspecified"],["uvz","application/vnd.dece.zip"],["vbox","application/x-virtualbox-vbox"],["vbox-extpack","application/x-virtualbox-vbox-extpack"],["vcard","text/vcard"],["vcd","application/x-cdlink"],["vcf","text/x-vcard"],["vcg","application/vnd.groove-vcard"],["vcs","text/x-vcalendar"],["vcx","application/vnd.vcx"],["vdi","application/x-virtualbox-vdi"],["vds","model/vnd.sap.vds"],["vhd","application/x-virtualbox-vhd"],["vis","application/vnd.visionary"],["viv","video/vnd.vivo"],["vlc","application/videolan"],["vmdk","application/x-virtualbox-vmdk"],["vob","video/x-ms-vob"],["vor","application/vnd.stardivision.writer"],["vox","application/x-authorware-bin"],["vrml","model/vrml"],["vsd","application/vnd.visio"],["vsf","application/vnd.vsf"],["vss","application/vnd.visio"],["vst","application/vnd.visio"],["vsw","application/vnd.visio"],["vtf","image/vnd.valve.source.texture"],["vtt","text/vtt"],["vtu","model/vnd.vtu"],["vxml","application/voicexml+xml"],["w3d","application/x-director"],["wad","application/x-doom"],["wadl","application/vnd.sun.wadl+xml"],["war","application/java-archive"],["wasm","application/wasm"],["wav","audio/x-wav"],["wax","audio/x-ms-wax"],["wbmp","image/vnd.wap.wbmp"],["wbs","application/vnd.criticaltools.wbs+xml"],["wbxml","application/wbxml"],["wcm","application/vnd.ms-works"],["wdb","application/vnd.ms-works"],["wdp","image/vnd.ms-photo"],["weba","audio/webm"],["webapp","application/x-web-app-manifest+json"],["webm","video/webm"],["webmanifest","application/manifest+json"],["webp","image/webp"],["wg","application/vnd.pmi.widget"],["wgt","application/widget"],["wks","application/vnd.ms-works"],["wm","video/x-ms-wm"],["wma","audio/x-ms-wma"],["wmd","application/x-ms-wmd"],["wmf","image/wmf"],["wml","text/vnd.wap.wml"],["wmlc","application/wmlc"],["wmls","text/vnd.wap.wmlscript"],["wmlsc","application/vnd.wap.wmlscriptc"],["wmv","video/x-ms-wmv"],["wmx","video/x-ms-wmx"],["wmz","application/x-msmetafile"],["woff","font/woff"],["woff2","font/woff2"],["word","application/msword"],["wpd","application/vnd.wordperfect"],["wpl","application/vnd.ms-wpl"],["wps","application/vnd.ms-works"],["wqd","application/vnd.wqd"],["wri","application/x-mswrite"],["wrl","model/vrml"],["wsc","message/vnd.wfa.wsc"],["wsdl","application/wsdl+xml"],["wspolicy","application/wspolicy+xml"],["wtb","application/vnd.webturbo"],["wvx","video/x-ms-wvx"],["x3d","model/x3d+xml"],["x3db","model/x3d+fastinfoset"],["x3dbz","model/x3d+binary"],["x3dv","model/x3d-vrml"],["x3dvz","model/x3d+vrml"],["x3dz","model/x3d+xml"],["x32","application/x-authorware-bin"],["x_b","model/vnd.parasolid.transmit.binary"],["x_t","model/vnd.parasolid.transmit.text"],["xaml","application/xaml+xml"],["xap","application/x-silverlight-app"],["xar","application/vnd.xara"],["xav","application/xcap-att+xml"],["xbap","application/x-ms-xbap"],["xbd","application/vnd.fujixerox.docuworks.binder"],["xbm","image/x-xbitmap"],["xca","application/xcap-caps+xml"],["xcs","application/calendar+xml"],["xdf","application/xcap-diff+xml"],["xdm","application/vnd.syncml.dm+xml"],["xdp","application/vnd.adobe.xdp+xml"],["xdssc","application/dssc+xml"],["xdw","application/vnd.fujixerox.docuworks"],["xel","application/xcap-el+xml"],["xenc","application/xenc+xml"],["xer","application/patch-ops-error+xml"],["xfdf","application/vnd.adobe.xfdf"],["xfdl","application/vnd.xfdl"],["xht","application/xhtml+xml"],["xhtml","application/xhtml+xml"],["xhvml","application/xv+xml"],["xif","image/vnd.xiff"],["xl","application/excel"],["xla","application/vnd.ms-excel"],["xlam","application/vnd.ms-excel.addin.macroEnabled.12"],["xlc","application/vnd.ms-excel"],["xlf","application/xliff+xml"],["xlm","application/vnd.ms-excel"],["xls","application/vnd.ms-excel"],["xlsb","application/vnd.ms-excel.sheet.binary.macroEnabled.12"],["xlsm","application/vnd.ms-excel.sheet.macroEnabled.12"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xlt","application/vnd.ms-excel"],["xltm","application/vnd.ms-excel.template.macroEnabled.12"],["xltx","application/vnd.openxmlformats-officedocument.spreadsheetml.template"],["xlw","application/vnd.ms-excel"],["xm","audio/xm"],["xml","application/xml"],["xns","application/xcap-ns+xml"],["xo","application/vnd.olpc-sugar"],["xop","application/xop+xml"],["xpi","application/x-xpinstall"],["xpl","application/xproc+xml"],["xpm","image/x-xpixmap"],["xpr","application/vnd.is-xpr"],["xps","application/vnd.ms-xpsdocument"],["xpw","application/vnd.intercon.formnet"],["xpx","application/vnd.intercon.formnet"],["xsd","application/xml"],["xsl","application/xml"],["xslt","application/xslt+xml"],["xsm","application/vnd.syncml+xml"],["xspf","application/xspf+xml"],["xul","application/vnd.mozilla.xul+xml"],["xvm","application/xv+xml"],["xvml","application/xv+xml"],["xwd","image/x-xwindowdump"],["xyz","chemical/x-xyz"],["xz","application/x-xz"],["yaml","text/yaml"],["yang","application/yang"],["yin","application/yin+xml"],["yml","text/yaml"],["ymp","text/x-suse-ymp"],["z","application/x-compress"],["z1","application/x-zmachine"],["z2","application/x-zmachine"],["z3","application/x-zmachine"],["z4","application/x-zmachine"],["z5","application/x-zmachine"],["z6","application/x-zmachine"],["z7","application/x-zmachine"],["z8","application/x-zmachine"],["zaz","application/vnd.zzazz.deck+xml"],["zip","application/zip"],["zir","application/vnd.zul"],["zirz","application/vnd.zul"],["zmm","application/vnd.handheld-entertainment+xml"],["zsh","text/x-scriptzsh"]]);function br(i,r,o){const a=Mg(i),{webkitRelativePath:u}=i,d=typeof r=="string"?r:typeof u=="string"&&u.length>0?u:`./${i.name}`;return typeof a.path!="string"&&Yp(a,"path",d),Yp(a,"relativePath",d),a}function Mg(i){const{name:r}=i;if(r&&r.lastIndexOf(".")!==-1&&!i.type){const a=r.split(".").pop().toLowerCase(),u=Bg.get(a);u&&Object.defineProperty(i,"type",{value:u,writable:!1,configurable:!1,enumerable:!0})}return i}function Yp(i,r,o){Object.defineProperty(i,r,{value:o,writable:!1,configurable:!1,enumerable:!0})}const Ug=[".DS_Store","Thumbs.db"];function qg(i){return Yn(this,void 0,void 0,function*(){return Jo(i)&&$g(i.dataTransfer)?Kg(i.dataTransfer,i.type):Hg(i)?Vg(i):Array.isArray(i)&&i.every(r=>"getFile"in r&&typeof r.getFile=="function")?Wg(i):[]})}function $g(i){return Jo(i)}function Hg(i){return Jo(i)&&Jo(i.target)}function Jo(i){return typeof i=="object"&&i!==null}function Vg(i){return wl(i.target.files).map(r=>br(r))}function Wg(i){return Yn(this,void 0,void 0,function*(){return(yield Promise.all(i.map(o=>o.getFile()))).map(o=>br(o))})}function Kg(i,r){return Yn(this,void 0,void 0,function*(){if(i.items){const o=wl(i.items).filter(u=>u.kind==="file");if(r!=="drop")return o;const a=yield Promise.all(o.map(Qg));return Xp(Jd(a))}return Xp(wl(i.files).map(o=>br(o)))})}function Xp(i){return i.filter(r=>Ug.indexOf(r.name)===-1)}function wl(i){if(i===null)return[];const r=[];for(let o=0;o<i.length;o++){const a=i[o];r.push(a)}return r}function Qg(i){if(typeof i.webkitGetAsEntry!="function")return Gp(i);const r=i.webkitGetAsEntry();return r&&r.isDirectory?Yd(r):Gp(i,r)}function Jd(i){return i.reduce((r,o)=>[...r,...Array.isArray(o)?Jd(o):[o]],[])}function Gp(i,r){return Yn(this,void 0,void 0,function*(){var o;if(globalThis.isSecureContext&&typeof i.getAsFileSystemHandle=="function"){const d=yield i.getAsFileSystemHandle();if(d===null)throw new Error(`${i} is not a File`);if(d!==void 0){const f=yield d.getFile();return f.handle=d,br(f)}}const a=i.getAsFile();if(!a)throw new Error(`${i} is not a File`);return br(a,(o=r==null?void 0:r.fullPath)!==null&&o!==void 0?o:void 0)})}function Jg(i){return Yn(this,void 0,void 0,function*(){return i.isDirectory?Yd(i):Yg(i)})}function Yd(i){const r=i.createReader();return new Promise((o,a)=>{const u=[];function d(){r.readEntries(f=>Yn(this,void 0,void 0,function*(){if(f.length){const g=Promise.all(f.map(Jg));u.push(g),d()}else try{const g=yield Promise.all(u);o(g)}catch(g){a(g)}}),f=>{a(f)})}d()})}function Yg(i){return Yn(this,void 0,void 0,function*(){return new Promise((r,o)=>{i.file(a=>{const u=br(a,i.fullPath);r(u)},a=>{o(a)})})})}var Fo={},Zp;function Xg(){return Zp||(Zp=1,Fo.__esModule=!0,Fo.default=function(i,r){if(i&&r){var o=Array.isArray(r)?r:r.split(",");if(o.length===0)return!0;var a=i.name||"",u=(i.type||"").toLowerCase(),d=u.replace(/\/.*$/,"");return o.some(function(f){var g=f.trim().toLowerCase();return g.charAt(0)==="."?a.toLowerCase().endsWith(g):g.endsWith("/*")?d===g.replace(/\/.*$/,""):u===g})}return!0}),Fo}var Gg=Xg();const ol=Cl(Gg);function ed(i){return ty(i)||ey(i)||Gd(i)||Zg()}function Zg(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function ey(i){if(typeof Symbol<"u"&&i[Symbol.iterator]!=null||i["@@iterator"]!=null)return Array.from(i)}function ty(i){if(Array.isArray(i))return kl(i)}function td(i,r){var o=Object.keys(i);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(i);r&&(a=a.filter(function(u){return Object.getOwnPropertyDescriptor(i,u).enumerable})),o.push.apply(o,a)}return o}function nd(i){for(var r=1;r<arguments.length;r++){var o=arguments[r]!=null?arguments[r]:{};r%2?td(Object(o),!0).forEach(function(a){Xd(i,a,o[a])}):Object.getOwnPropertyDescriptors?Object.defineProperties(i,Object.getOwnPropertyDescriptors(o)):td(Object(o)).forEach(function(a){Object.defineProperty(i,a,Object.getOwnPropertyDescriptor(o,a))})}return i}function Xd(i,r,o){return r in i?Object.defineProperty(i,r,{value:o,enumerable:!0,configurable:!0,writable:!0}):i[r]=o,i}function Si(i,r){return iy(i)||ry(i,r)||Gd(i,r)||ny()}function ny(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Gd(i,r){if(i){if(typeof i=="string")return kl(i,r);var o=Object.prototype.toString.call(i).slice(8,-1);if(o==="Object"&&i.constructor&&(o=i.constructor.name),o==="Map"||o==="Set")return Array.from(i);if(o==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o))return kl(i,r)}}function kl(i,r){(r==null||r>i.length)&&(r=i.length);for(var o=0,a=new Array(r);o<r;o++)a[o]=i[o];return a}function ry(i,r){var o=i==null?null:typeof Symbol<"u"&&i[Symbol.iterator]||i["@@iterator"];if(o!=null){var a=[],u=!0,d=!1,f,g;try{for(o=o.call(i);!(u=(f=o.next()).done)&&(a.push(f.value),!(r&&a.length===r));u=!0);}catch(E){d=!0,g=E}finally{try{!u&&o.return!=null&&o.return()}finally{if(d)throw g}}return a}}function iy(i){if(Array.isArray(i))return i}var oy=typeof ol=="function"?ol:ol.default,sy="file-invalid-type",ay="file-too-large",ly="file-too-small",cy="too-many-files",uy=function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",o=r.split(","),a=o.length>1?"one of ".concat(o.join(", ")):o[0];return{code:sy,message:"File type must be ".concat(a)}},rd=function(r){return{code:ay,message:"File is larger than ".concat(r," ").concat(r===1?"byte":"bytes")}},id=function(r){return{code:ly,message:"File is smaller than ".concat(r," ").concat(r===1?"byte":"bytes")}},py={code:cy,message:"Too many files"};function Zd(i,r){var o=i.type==="application/x-moz-file"||oy(i,r);return[o,o?null:uy(r)]}function ef(i,r,o){if(Wn(i.size))if(Wn(r)&&Wn(o)){if(i.size>o)return[!1,rd(o)];if(i.size<r)return[!1,id(r)]}else{if(Wn(r)&&i.size<r)return[!1,id(r)];if(Wn(o)&&i.size>o)return[!1,rd(o)]}return[!0,null]}function Wn(i){return i!=null}function dy(i){var r=i.files,o=i.accept,a=i.minSize,u=i.maxSize,d=i.multiple,f=i.maxFiles,g=i.validator;return!d&&r.length>1||d&&f>=1&&r.length>f?!1:r.every(function(E){var _=Zd(E,o),b=Si(_,1),N=b[0],z=ef(E,a,u),J=Si(z,1),D=J[0],L=g?g(E):null;return N&&D&&!L})}function Yo(i){return typeof i.isPropagationStopped=="function"?i.isPropagationStopped():typeof i.cancelBubble<"u"?i.cancelBubble:!1}function Io(i){return i.dataTransfer?Array.prototype.some.call(i.dataTransfer.types,function(r){return r==="Files"||r==="application/x-moz-file"}):!!i.target&&!!i.target.files}function od(i){i.preventDefault()}function fy(i){return i.indexOf("MSIE")!==-1||i.indexOf("Trident/")!==-1}function my(i){return i.indexOf("Edge/")!==-1}function hy(){var i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:window.navigator.userAgent;return fy(i)||my(i)}function Ht(){for(var i=arguments.length,r=new Array(i),o=0;o<i;o++)r[o]=arguments[o];return function(a){for(var u=arguments.length,d=new Array(u>1?u-1:0),f=1;f<u;f++)d[f-1]=arguments[f];return r.some(function(g){return!Yo(a)&&g&&g.apply(void 0,[a].concat(d)),Yo(a)})}}function vy(){return"showOpenFilePicker"in window}function gy(i){if(Wn(i)){var r=Object.entries(i).filter(function(o){var a=Si(o,2),u=a[0],d=a[1],f=!0;return tf(u)||(console.warn('Skipped "'.concat(u,'" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.')),f=!1),(!Array.isArray(d)||!d.every(nf))&&(console.warn('Skipped "'.concat(u,'" because an invalid file extension was provided.')),f=!1),f}).reduce(function(o,a){var u=Si(a,2),d=u[0],f=u[1];return nd(nd({},o),{},Xd({},d,f))},{});return[{description:"Files",accept:r}]}return i}function yy(i){if(Wn(i))return Object.entries(i).reduce(function(r,o){var a=Si(o,2),u=a[0],d=a[1];return[].concat(ed(r),[u],ed(d))},[]).filter(function(r){return tf(r)||nf(r)}).join(",")}function xy(i){return i instanceof DOMException&&(i.name==="AbortError"||i.code===i.ABORT_ERR)}function wy(i){return i instanceof DOMException&&(i.name==="SecurityError"||i.code===i.SECURITY_ERR)}function tf(i){return i==="audio/*"||i==="video/*"||i==="image/*"||i==="text/*"||i==="application/*"||/\w+\/[-+.\w]+/g.test(i)}function nf(i){return/^.*\.[\w]+$/.test(i)}var ky=["children"],Sy=["open"],Ey=["refKey","role","onKeyDown","onFocus","onBlur","onClick","onDragEnter","onDragOver","onDragLeave","onDrop"],_y=["refKey","onChange","onClick"];function by(i){return jy(i)||Ny(i)||rf(i)||Cy()}function Cy(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Ny(i){if(typeof Symbol<"u"&&i[Symbol.iterator]!=null||i["@@iterator"]!=null)return Array.from(i)}function jy(i){if(Array.isArray(i))return Sl(i)}function sl(i,r){return Oy(i)||Ty(i,r)||rf(i,r)||Ry()}function Ry(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function rf(i,r){if(i){if(typeof i=="string")return Sl(i,r);var o=Object.prototype.toString.call(i).slice(8,-1);if(o==="Object"&&i.constructor&&(o=i.constructor.name),o==="Map"||o==="Set")return Array.from(i);if(o==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o))return Sl(i,r)}}function Sl(i,r){(r==null||r>i.length)&&(r=i.length);for(var o=0,a=new Array(r);o<r;o++)a[o]=i[o];return a}function Ty(i,r){var o=i==null?null:typeof Symbol<"u"&&i[Symbol.iterator]||i["@@iterator"];if(o!=null){var a=[],u=!0,d=!1,f,g;try{for(o=o.call(i);!(u=(f=o.next()).done)&&(a.push(f.value),!(r&&a.length===r));u=!0);}catch(E){d=!0,g=E}finally{try{!u&&o.return!=null&&o.return()}finally{if(d)throw g}}return a}}function Oy(i){if(Array.isArray(i))return i}function sd(i,r){var o=Object.keys(i);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(i);r&&(a=a.filter(function(u){return Object.getOwnPropertyDescriptor(i,u).enumerable})),o.push.apply(o,a)}return o}function Pe(i){for(var r=1;r<arguments.length;r++){var o=arguments[r]!=null?arguments[r]:{};r%2?sd(Object(o),!0).forEach(function(a){El(i,a,o[a])}):Object.getOwnPropertyDescriptors?Object.defineProperties(i,Object.getOwnPropertyDescriptors(o)):sd(Object(o)).forEach(function(a){Object.defineProperty(i,a,Object.getOwnPropertyDescriptor(o,a))})}return i}function El(i,r,o){return r in i?Object.defineProperty(i,r,{value:o,enumerable:!0,configurable:!0,writable:!0}):i[r]=o,i}function Xo(i,r){if(i==null)return{};var o=Py(i,r),a,u;if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(i);for(u=0;u<d.length;u++)a=d[u],!(r.indexOf(a)>=0)&&Object.prototype.propertyIsEnumerable.call(i,a)&&(o[a]=i[a])}return o}function Py(i,r){if(i==null)return{};var o={},a=Object.keys(i),u,d;for(d=0;d<a.length;d++)u=a[d],!(r.indexOf(u)>=0)&&(o[u]=i[u]);return o}var Ll=ee.forwardRef(function(i,r){var o=i.children,a=Xo(i,ky),u=sf(a),d=u.open,f=Xo(u,Sy);return ee.useImperativeHandle(r,function(){return{open:d}},[d]),oh.createElement(ee.Fragment,null,o(Pe(Pe({},f),{},{open:d})))});Ll.displayName="Dropzone";var of={disabled:!1,getFilesFromEvent:qg,maxSize:1/0,minSize:0,multiple:!0,maxFiles:0,preventDropOnDocument:!0,noClick:!1,noKeyboard:!1,noDrag:!1,noDragEventsBubbling:!1,validator:null,useFsAccessApi:!1,autoFocus:!1};Ll.defaultProps=of;Ll.propTypes={children:we.func,accept:we.objectOf(we.arrayOf(we.string)),multiple:we.bool,preventDropOnDocument:we.bool,noClick:we.bool,noKeyboard:we.bool,noDrag:we.bool,noDragEventsBubbling:we.bool,minSize:we.number,maxSize:we.number,maxFiles:we.number,disabled:we.bool,getFilesFromEvent:we.func,onFileDialogCancel:we.func,onFileDialogOpen:we.func,useFsAccessApi:we.bool,autoFocus:we.bool,onDragEnter:we.func,onDragLeave:we.func,onDragOver:we.func,onDrop:we.func,onDropAccepted:we.func,onDropRejected:we.func,onError:we.func,validator:we.func};var _l={isFocused:!1,isFileDialogActive:!1,isDragActive:!1,isDragAccept:!1,isDragReject:!1,acceptedFiles:[],fileRejections:[]};function sf(){var i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=Pe(Pe({},of),i),o=r.accept,a=r.disabled,u=r.getFilesFromEvent,d=r.maxSize,f=r.minSize,g=r.multiple,E=r.maxFiles,_=r.onDragEnter,b=r.onDragLeave,N=r.onDragOver,z=r.onDrop,J=r.onDropAccepted,D=r.onDropRejected,L=r.onFileDialogCancel,P=r.onFileDialogOpen,se=r.useFsAccessApi,Q=r.autoFocus,B=r.preventDropOnDocument,U=r.noClick,ne=r.noKeyboard,ce=r.noDrag,ue=r.noDragEventsBubbling,ke=r.onError,ze=r.validator,Je=ee.useMemo(function(){return yy(o)},[o]),ae=ee.useMemo(function(){return gy(o)},[o]),Ie=ee.useMemo(function(){return typeof P=="function"?P:ad},[P]),Be=ee.useMemo(function(){return typeof L=="function"?L:ad},[L]),ye=ee.useRef(null),je=ee.useRef(null),Ge=ee.useReducer(Ay,_l),_e=sl(Ge,2),F=_e[0],V=_e[1],q=F.isFocused,x=F.isFileDialogActive,j=ee.useRef(typeof window<"u"&&window.isSecureContext&&se&&vy()),re=function(){!j.current&&x&&setTimeout(function(){if(je.current){var G=je.current.files;G.length||(V({type:"closeDialog"}),Be())}},300)};ee.useEffect(function(){return window.addEventListener("focus",re,!1),function(){window.removeEventListener("focus",re,!1)}},[je,x,Be,j]);var te=ee.useRef([]),pe=function(G){ye.current&&ye.current.contains(G.target)||(G.preventDefault(),te.current=[])};ee.useEffect(function(){return B&&(document.addEventListener("dragover",od,!1),document.addEventListener("drop",pe,!1)),function(){B&&(document.removeEventListener("dragover",od),document.removeEventListener("drop",pe))}},[ye,B]),ee.useEffect(function(){return!a&&Q&&ye.current&&ye.current.focus(),function(){}},[ye,Q,a]);var oe=ee.useCallback(function(I){ke?ke(I):console.error(I)},[ke]),ve=ee.useCallback(function(I){I.preventDefault(),I.persist(),Ft(I),te.current=[].concat(by(te.current),[I.target]),Io(I)&&Promise.resolve(u(I)).then(function(G){if(!(Yo(I)&&!ue)){var Re=G.length,Ae=Re>0&&dy({files:G,accept:Je,minSize:f,maxSize:d,multiple:g,maxFiles:E,validator:ze}),Ve=Re>0&&!Ae;V({isDragAccept:Ae,isDragReject:Ve,isDragActive:!0,type:"setDraggedFiles"}),_&&_(I)}}).catch(function(G){return oe(G)})},[u,_,oe,ue,Je,f,d,g,E,ze]),me=ee.useCallback(function(I){I.preventDefault(),I.persist(),Ft(I);var G=Io(I);if(G&&I.dataTransfer)try{I.dataTransfer.dropEffect="copy"}catch{}return G&&N&&N(I),!1},[N,ue]),xe=ee.useCallback(function(I){I.preventDefault(),I.persist(),Ft(I);var G=te.current.filter(function(Ae){return ye.current&&ye.current.contains(Ae)}),Re=G.indexOf(I.target);Re!==-1&&G.splice(Re,1),te.current=G,!(G.length>0)&&(V({type:"setDraggedFiles",isDragActive:!1,isDragAccept:!1,isDragReject:!1}),Io(I)&&b&&b(I))},[ye,b,ue]),He=ee.useCallback(function(I,G){var Re=[],Ae=[];I.forEach(function(Ve){var sn=Zd(Ve,Je),xt=sl(sn,2),Qt=xt[0],Pr=xt[1],Zn=ef(Ve,f,d),jn=sl(Zn,2),Ar=jn[0],Rn=jn[1],Tn=ze?ze(Ve):null;if(Qt&&Ar&&!Tn)Re.push(Ve);else{var On=[Pr,Rn];Tn&&(On=On.concat(Tn)),Ae.push({file:Ve,errors:On.filter(function(Dr){return Dr})})}}),(!g&&Re.length>1||g&&E>=1&&Re.length>E)&&(Re.forEach(function(Ve){Ae.push({file:Ve,errors:[py]})}),Re.splice(0)),V({acceptedFiles:Re,fileRejections:Ae,isDragReject:Ae.length>0,type:"setFiles"}),z&&z(Re,Ae,G),Ae.length>0&&D&&D(Ae,G),Re.length>0&&J&&J(Re,G)},[V,g,Je,f,d,E,z,J,D,ze]),Wt=ee.useCallback(function(I){I.preventDefault(),I.persist(),Ft(I),te.current=[],Io(I)&&Promise.resolve(u(I)).then(function(G){Yo(I)&&!ue||He(G,I)}).catch(function(G){return oe(G)}),V({type:"reset"})},[u,He,oe,ue]),Kt=ee.useCallback(function(){if(j.current){V({type:"openDialog"}),Ie();var I={multiple:g,types:ae};window.showOpenFilePicker(I).then(function(G){return u(G)}).then(function(G){He(G,null),V({type:"closeDialog"})}).catch(function(G){xy(G)?(Be(G),V({type:"closeDialog"})):wy(G)?(j.current=!1,je.current?(je.current.value=null,je.current.click()):oe(new Error("Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided."))):oe(G)});return}je.current&&(V({type:"openDialog"}),Ie(),je.current.value=null,je.current.click())},[V,Ie,Be,se,He,oe,ae,g]),Nn=ee.useCallback(function(I){!ye.current||!ye.current.isEqualNode(I.target)||(I.key===" "||I.key==="Enter"||I.keyCode===32||I.keyCode===13)&&(I.preventDefault(),Kt())},[ye,Kt]),Xn=ee.useCallback(function(){V({type:"focus"})},[]),Rr=ee.useCallback(function(){V({type:"blur"})},[]),Tr=ee.useCallback(function(){U||(hy()?setTimeout(Kt,0):Kt())},[U,Kt]),Lt=function(G){return a?null:G},Gn=function(G){return ne?null:Lt(G)},rn=function(G){return ce?null:Lt(G)},Ft=function(G){ue&&G.stopPropagation()},on=ee.useMemo(function(){return function(){var I=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},G=I.refKey,Re=G===void 0?"ref":G,Ae=I.role,Ve=I.onKeyDown,sn=I.onFocus,xt=I.onBlur,Qt=I.onClick,Pr=I.onDragEnter,Zn=I.onDragOver,jn=I.onDragLeave,Ar=I.onDrop,Rn=Xo(I,Ey);return Pe(Pe(El({onKeyDown:Gn(Ht(Ve,Nn)),onFocus:Gn(Ht(sn,Xn)),onBlur:Gn(Ht(xt,Rr)),onClick:Lt(Ht(Qt,Tr)),onDragEnter:rn(Ht(Pr,ve)),onDragOver:rn(Ht(Zn,me)),onDragLeave:rn(Ht(jn,xe)),onDrop:rn(Ht(Ar,Wt)),role:typeof Ae=="string"&&Ae!==""?Ae:"presentation"},Re,ye),!a&&!ne?{tabIndex:0}:{}),Rn)}},[ye,Nn,Xn,Rr,Tr,ve,me,xe,Wt,ne,ce,a]),Or=ee.useCallback(function(I){I.stopPropagation()},[]),bi=ee.useMemo(function(){return function(){var I=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},G=I.refKey,Re=G===void 0?"ref":G,Ae=I.onChange,Ve=I.onClick,sn=Xo(I,_y),xt=El({accept:Je,multiple:g,type:"file",style:{border:0,clip:"rect(0, 0, 0, 0)",clipPath:"inset(50%)",height:"1px",margin:"0 -1px -1px 0",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"},onChange:Lt(Ht(Ae,Wt)),onClick:Lt(Ht(Ve,Or)),tabIndex:-1},Re,je);return Pe(Pe({},xt),sn)}},[je,o,g,Wt,a]);return Pe(Pe({},F),{},{isFocused:q&&!a,getRootProps:on,getInputProps:bi,rootRef:ye,inputRef:je,open:Lt(Kt)})}function Ay(i,r){switch(r.type){case"focus":return Pe(Pe({},i),{},{isFocused:!0});case"blur":return Pe(Pe({},i),{},{isFocused:!1});case"openDialog":return Pe(Pe({},_l),{},{isFileDialogActive:!0});case"closeDialog":return Pe(Pe({},i),{},{isFileDialogActive:!1});case"setDraggedFiles":return Pe(Pe({},i),{},{isDragActive:r.isDragActive,isDragAccept:r.isDragAccept,isDragReject:r.isDragReject});case"setFiles":return Pe(Pe({},i),{},{acceptedFiles:r.acceptedFiles,fileRejections:r.fileRejections,isDragReject:r.isDragReject});case"reset":return Pe({},_l);default:return i}}function ad(){}/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dy=i=>i.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),af=(...i)=>i.filter((r,o,a)=>!!r&&r.trim()!==""&&a.indexOf(r)===o).join(" ").trim();/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var zy={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ly=ee.forwardRef(({color:i="currentColor",size:r=24,strokeWidth:o=2,absoluteStrokeWidth:a,className:u="",children:d,iconNode:f,...g},E)=>ee.createElement("svg",{ref:E,...zy,width:r,height:r,stroke:i,strokeWidth:a?Number(o)*24/Number(r):o,className:af("lucide",u),...g},[...f.map(([_,b])=>ee.createElement(_,b)),...Array.isArray(d)?d:[d]]));/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const it=(i,r)=>{const o=ee.forwardRef(({className:a,...u},d)=>ee.createElement(Ly,{ref:d,iconNode:r,className:af(`lucide-${Dy(i)}`,a),...u}));return o.displayName=`${i}`,o};/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fy=it("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const al=it("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ld=it("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cd=it("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Iy=it("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const By=it("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const My=it("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Uy=it("File",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bl=it("FolderOpen",[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ud=it("Pause",[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qy=it("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $y=it("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hy=it("Timer",[["line",{x1:"10",x2:"14",y1:"2",y2:"2",key:"14vaq8"}],["line",{x1:"12",x2:"15",y1:"14",y2:"11",key:"17fdiu"}],["circle",{cx:"12",cy:"14",r:"8",key:"1e1u0o"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vy=it("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wy=it("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ky=it("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),Qy=({onFilesSelected:i,isUploading:r})=>{const[o,a]=ee.useState([]),u=ee.useCallback(z=>{const J=z.filter(D=>D.type==="application/pdf"||D.name.toLowerCase().endsWith(".pdf"));a(D=>[...D,...J])},[]),{getRootProps:d,getInputProps:f,isDragActive:g}=sf({onDrop:u,accept:{"application/pdf":[".pdf"]},multiple:!0,disabled:r}),E=z=>{a(J=>J.filter((D,L)=>L!==z))},_=()=>{o.length>0&&i(o)},b=()=>{a([])},N=z=>{if(z===0)return"0 Bytes";const J=1024,D=["Bytes","KB","MB","GB"],L=Math.floor(Math.log(z)/Math.log(J));return parseFloat((z/Math.pow(J,L)).toFixed(2))+" "+D[L]};return v.jsxs("div",{className:"w-full",children:[v.jsxs("div",{...d(),className:`
          border-2 border-dashed rounded-macos-lg p-12 text-center cursor-pointer transition-all duration-300
          ${g?"border-macos-blue bg-macos-blue/5 shadow-macos scale-[1.02]":"border-macos-gray/30 hover:border-macos-blue/50 hover:bg-macos-surface"}
          ${r?"opacity-50 cursor-not-allowed":""}
          backdrop-blur-sm
        `,children:[v.jsx("input",{...f()}),v.jsx("div",{className:"flex flex-col items-center space-y-6",children:g?v.jsxs(v.Fragment,{children:[v.jsx("div",{className:"w-20 h-20 bg-macos-blue rounded-macos-lg flex items-center justify-center shadow-macos animate-macos-bounce",children:v.jsx(bl,{className:"w-10 h-10 text-white"})}),v.jsx("p",{className:"text-xl font-semibold text-macos-blue",children:"释放文件到这里..."})]}):v.jsxs(v.Fragment,{children:[v.jsx("div",{className:"w-20 h-20 bg-macos-surface rounded-macos-lg flex items-center justify-center shadow-macos border border-white/30",children:v.jsx(Vy,{className:"w-10 h-10 text-macos-gray"})}),v.jsxs("div",{className:"space-y-2",children:[v.jsx("p",{className:"text-xl font-semibold text-gray-800",children:"拖拽PDF文件到这里，或点击选择文件"}),v.jsx("p",{className:"text-macos-gray",children:"支持批量上传多个PDF文件"})]})]})})]}),o.length>0&&v.jsxs("div",{className:"mt-8",children:[v.jsxs("div",{className:"flex justify-between items-center mb-6",children:[v.jsxs("h3",{className:"text-xl font-semibold text-gray-800",children:["已选择文件 (",o.length,")"]}),v.jsx("button",{onClick:b,className:"px-4 py-2 text-sm font-medium text-macos-red bg-macos-red/10 rounded-macos border border-macos-red/20 hover:bg-macos-red/20 transition-all duration-200 disabled:opacity-50",disabled:r,children:"清空所有"})]}),v.jsx("div",{className:"max-h-80 overflow-y-auto bg-macos-surface backdrop-blur-sm rounded-macos-lg border border-white/30 shadow-macos",children:o.map((z,J)=>v.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-white/20 last:border-b-0 hover:bg-white/30 transition-colors duration-200",children:[v.jsxs("div",{className:"flex items-center space-x-4",children:[v.jsx("div",{className:"w-10 h-10 bg-macos-red/10 rounded-macos flex items-center justify-center",children:v.jsx(Uy,{className:"w-5 h-5 text-macos-red"})}),v.jsxs("div",{children:[v.jsx("p",{className:"text-sm font-semibold text-gray-800 truncate max-w-xs",children:z.name}),v.jsx("p",{className:"text-xs text-macos-gray",children:N(z.size)})]})]}),v.jsx("button",{onClick:()=>E(J),className:"w-8 h-8 bg-macos-red/10 rounded-macos flex items-center justify-center text-macos-red hover:bg-macos-red/20 transition-all duration-200 disabled:opacity-50",disabled:r,children:v.jsx(Wy,{className:"w-4 h-4"})})]},J))})]}),o.length>0&&v.jsxs("div",{className:"mt-8 flex justify-center space-x-4",children:[v.jsx("button",{onClick:b,className:"px-8 py-3 bg-macos-surface backdrop-blur-sm border border-white/30 rounded-macos-lg text-gray-700 font-medium hover:bg-white/50 transition-all duration-200 shadow-macos disabled:opacity-50",disabled:r,children:"清空"}),v.jsx("button",{onClick:_,className:`
              px-8 py-3 rounded-macos-lg font-semibold transition-all duration-200 shadow-macos
              ${r?"bg-macos-gray text-white cursor-not-allowed":"bg-macos-blue text-white hover:bg-macos-blue-hover hover:shadow-macos-lg hover:scale-105 active:scale-95"}
            `,disabled:r||o.length===0,children:r?"上传中...":`开始转换 (${o.length} 个文件)`})]}),v.jsxs("div",{className:"mt-10 p-6 bg-macos-surface backdrop-blur-sm rounded-macos-lg border border-white/30 shadow-macos",children:[v.jsx("h4",{className:"font-semibold text-gray-800 mb-4 text-lg",children:"使用说明："}),v.jsxs("ul",{className:"text-macos-gray space-y-3 leading-relaxed",children:[v.jsxs("li",{className:"flex items-start space-x-2",children:[v.jsx("span",{className:"w-1.5 h-1.5 bg-macos-blue rounded-full mt-2 flex-shrink-0"}),v.jsx("span",{children:"支持拖拽整个文件夹或批量选择PDF文件"})]}),v.jsxs("li",{className:"flex items-start space-x-2",children:[v.jsx("span",{className:"w-1.5 h-1.5 bg-macos-blue rounded-full mt-2 flex-shrink-0"}),v.jsx("span",{children:"系统会自动按文件名中的页码排序（如：文档_1.pdf, 文档_2.pdf）"})]}),v.jsxs("li",{className:"flex items-start space-x-2",children:[v.jsx("span",{className:"w-1.5 h-1.5 bg-macos-blue rounded-full mt-2 flex-shrink-0"}),v.jsx("span",{children:"转换完成后会生成单独的Markdown和JSON文件，以及合并后的完整文档"})]}),v.jsxs("li",{className:"flex items-start space-x-2",children:[v.jsx("span",{className:"w-1.5 h-1.5 bg-macos-blue rounded-full mt-2 flex-shrink-0"}),v.jsx("span",{children:"支持中文文件名和路径"})]})]})]})]})},Jy=({taskStatus:i,progress:r,onDownload:o,onReset:a,onOpenFolder:u,onPause:d,onResume:f})=>{const[g,E]=ee.useState(!1);if(!i)return null;const{status:_,total:b,completed:N,failed:z,errors:J,currentFile:D,resultFolderPath:L,resultFolderName:P,filesStatus:se=[],processingSpeed:Q=0,estimatedTimeRemaining:B=null}=i,U=b>0?Math.round((N+z)/b*100):0,ne=async()=>{if(L)try{await navigator.clipboard.writeText(L)}catch(ae){console.error("复制失败:",ae)}},ce=ae=>{if(!ae||ae===1/0)return"--";if(ae<1)return"< 1分钟";if(ae<60)return`${Math.round(ae)}分钟`;const Ie=Math.floor(ae/60),Be=Math.round(ae%60);return`${Ie}小时${Be}分钟`},ue=ae=>!ae||ae===0?"--":`${ae.toFixed(1)} 文件/分钟`,ke=()=>{switch(_){case"processing":return v.jsx(cd,{className:"w-6 h-6 text-macos-blue animate-spin"});case"paused":return v.jsx(ud,{className:"w-6 h-6 text-macos-orange"});case"completed":return v.jsx(al,{className:"w-6 h-6 text-macos-green"});case"failed":return v.jsx(ld,{className:"w-6 h-6 text-macos-red"});default:return v.jsx(cd,{className:"w-6 h-6 text-macos-gray"})}},ze=()=>{switch(_){case"processing":return"转换中...";case"paused":return"已暂停";case"completed":return"转换完成";case"failed":return"转换失败";default:return"准备中..."}},Je=()=>{switch(_){case"processing":return"text-macos-blue";case"paused":return"text-macos-orange";case"completed":return"text-macos-green";case"failed":return"text-macos-red";default:return"text-macos-gray"}};return v.jsx("div",{className:"w-full",children:v.jsxs("div",{className:"space-y-8",children:[v.jsxs("div",{className:"flex items-center justify-center space-x-4",children:[v.jsx("div",{className:"w-12 h-12 bg-macos-surface rounded-macos-lg flex items-center justify-center shadow-macos border border-white/30",children:ke()}),v.jsx("h2",{className:`text-2xl font-bold ${Je()} tracking-tight`,children:ze()})]}),v.jsxs("div",{className:"bg-macos-surface backdrop-blur-sm rounded-macos-lg p-6 border border-white/30 shadow-macos",children:[v.jsxs("div",{className:"flex justify-between text-sm text-macos-gray mb-4 font-medium",children:[v.jsx("span",{children:"总进度"}),v.jsxs("span",{children:[N+z," / ",b," (",U,"%)"]})]}),v.jsx("div",{className:"w-full bg-macos-gray-light rounded-full h-4 shadow-macos-inner mb-6",children:v.jsx("div",{className:"bg-gradient-to-r from-macos-blue to-macos-blue-hover h-4 rounded-full transition-all duration-500 ease-out shadow-macos",style:{width:`${U}%`}})}),v.jsxs("div",{className:"grid grid-cols-2 gap-6",children:[v.jsxs("div",{className:"space-y-3",children:[v.jsxs("div",{className:"flex items-center space-x-3",children:[v.jsx("div",{className:"w-8 h-8 bg-macos-blue/20 rounded-macos flex items-center justify-center",children:v.jsx(Ky,{className:"w-4 h-4 text-macos-blue"})}),v.jsxs("div",{children:[v.jsx("p",{className:"text-sm text-macos-gray",children:"处理速度"}),v.jsx("p",{className:"font-semibold text-gray-800",children:ue(Q)})]})]}),v.jsxs("div",{className:"flex items-center space-x-3",children:[v.jsx("div",{className:"w-8 h-8 bg-macos-orange/20 rounded-macos flex items-center justify-center",children:v.jsx(Hy,{className:"w-4 h-4 text-macos-orange"})}),v.jsxs("div",{children:[v.jsx("p",{className:"text-sm text-macos-gray",children:"预计剩余时间"}),v.jsx("p",{className:"font-semibold text-gray-800",children:ce(B)})]})]})]}),v.jsxs("div",{className:"space-y-3",children:[v.jsxs("div",{className:"flex items-center space-x-3",children:[v.jsx("div",{className:"w-8 h-8 bg-macos-green/20 rounded-macos flex items-center justify-center",children:v.jsx(al,{className:"w-4 h-4 text-macos-green"})}),v.jsxs("div",{children:[v.jsx("p",{className:"text-sm text-macos-gray",children:"成功完成"}),v.jsxs("p",{className:"font-semibold text-gray-800",children:[N," 个文件"]})]})]}),v.jsxs("div",{className:"flex items-center space-x-3",children:[v.jsx("div",{className:"w-8 h-8 bg-macos-red/20 rounded-macos flex items-center justify-center",children:v.jsx(ld,{className:"w-4 h-4 text-macos-red"})}),v.jsxs("div",{children:[v.jsx("p",{className:"text-sm text-macos-gray",children:"转换失败"}),v.jsxs("p",{className:"font-semibold text-gray-800",children:[z," 个文件"]})]})]})]})]})]}),(_==="processing"||_==="paused")&&D&&v.jsx("div",{className:"bg-macos-blue/10 backdrop-blur-sm rounded-macos-lg p-6 border border-macos-blue/20 shadow-macos",children:v.jsxs("div",{className:"flex items-center justify-between",children:[v.jsxs("p",{className:"text-macos-blue font-medium",children:[v.jsx("span",{className:"font-semibold",children:_==="paused"?"已暂停：":"正在处理："}),v.jsx("span",{className:"ml-2",children:D})]}),v.jsxs("div",{className:"flex space-x-2",children:[_==="processing"&&v.jsxs("button",{onClick:d,className:"flex items-center space-x-2 px-4 py-2 bg-macos-orange/20 text-macos-orange rounded-macos border border-macos-orange/30 hover:bg-macos-orange/30 transition-colors",children:[v.jsx(ud,{className:"w-4 h-4"}),v.jsx("span",{children:"暂停"})]}),_==="paused"&&v.jsxs("button",{onClick:f,className:"flex items-center space-x-2 px-4 py-2 bg-macos-green/20 text-macos-green rounded-macos border border-macos-green/30 hover:bg-macos-green/30 transition-colors",children:[v.jsx(qy,{className:"w-4 h-4"}),v.jsx("span",{children:"恢复"})]})]})]})}),se.length>0&&v.jsxs("div",{className:"bg-macos-surface backdrop-blur-sm rounded-macos-lg border border-white/30 shadow-macos",children:[v.jsx("div",{className:"p-6 border-b border-white/20",children:v.jsxs("div",{className:"flex items-center justify-between",children:[v.jsx("h3",{className:"font-semibold text-gray-800 text-lg",children:"文件处理状态"}),v.jsx("button",{onClick:()=>E(!g),className:"text-sm text-macos-blue hover:text-macos-blue-hover transition-colors",children:g?"隐藏详情":"显示详情"})]})}),g&&v.jsx("div",{className:"max-h-64 overflow-y-auto",children:se.map((ae,Ie)=>v.jsxs("div",{className:"p-4 border-b border-white/10 last:border-b-0",children:[v.jsxs("div",{className:"flex items-center justify-between",children:[v.jsxs("div",{className:"flex items-center space-x-3",children:[v.jsx("div",{className:`w-3 h-3 rounded-full ${ae.status==="completed"?"bg-macos-green":ae.status==="failed"?"bg-macos-red":ae.status==="processing"?"bg-macos-blue animate-pulse":"bg-macos-gray"}`}),v.jsx("span",{className:"text-sm font-medium text-gray-800 truncate max-w-xs",children:ae.filename})]}),v.jsxs("div",{className:"text-xs text-macos-gray",children:[ae.status==="waiting"&&"等待中",ae.status==="processing"&&"处理中...",ae.status==="completed"&&"已完成",ae.status==="failed"&&"失败"]})]}),ae.error&&v.jsxs("p",{className:"text-xs text-macos-red mt-2 ml-6",children:["错误: ",ae.error]})]},Ie))})]}),v.jsxs("div",{className:"grid grid-cols-3 gap-6",children:[v.jsxs("div",{className:"text-center p-6 bg-macos-surface backdrop-blur-sm rounded-macos-lg border border-white/30 shadow-macos",children:[v.jsx("div",{className:"text-3xl font-bold text-gray-800 mb-2",children:b}),v.jsx("div",{className:"text-sm text-macos-gray font-medium",children:"总文件数"})]}),v.jsxs("div",{className:"text-center p-6 bg-macos-green/10 backdrop-blur-sm rounded-macos-lg border border-macos-green/20 shadow-macos",children:[v.jsx("div",{className:"text-3xl font-bold text-macos-green mb-2",children:N}),v.jsx("div",{className:"text-sm text-macos-green font-medium",children:"成功转换"})]}),v.jsxs("div",{className:"text-center p-6 bg-macos-red/10 backdrop-blur-sm rounded-macos-lg border border-macos-red/20 shadow-macos",children:[v.jsx("div",{className:"text-3xl font-bold text-macos-red mb-2",children:z}),v.jsx("div",{className:"text-sm text-macos-red font-medium",children:"转换失败"})]})]}),J&&J.length>0&&v.jsxs("div",{className:"bg-macos-red/10 backdrop-blur-sm rounded-macos-lg p-6 border border-macos-red/20 shadow-macos",children:[v.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[v.jsx("div",{className:"w-8 h-8 bg-macos-red/20 rounded-macos flex items-center justify-center",children:v.jsx(Fy,{className:"w-5 h-5 text-macos-red"})}),v.jsx("h3",{className:"font-semibold text-macos-red text-lg",children:"转换错误"})]}),v.jsx("div",{className:"max-h-40 overflow-y-auto space-y-3",children:J.map((ae,Ie)=>v.jsxs("div",{className:"text-sm text-macos-red bg-white/30 rounded-macos p-3",children:[v.jsxs("span",{className:"font-semibold",children:[ae.file,":"]}),v.jsx("span",{className:"ml-2",children:ae.error})]},Ie))})]}),(_==="completed"||_==="processing")&&L&&v.jsxs("div",{className:"bg-macos-surface backdrop-blur-sm rounded-macos-lg p-6 border border-white/30 shadow-macos",children:[v.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[v.jsx("div",{className:"w-8 h-8 bg-macos-blue/20 rounded-macos flex items-center justify-center",children:v.jsx(bl,{className:"w-5 h-5 text-macos-blue"})}),v.jsx("h3",{className:"font-semibold text-gray-800 text-lg",children:"结果文件夹"})]}),v.jsxs("div",{className:"space-y-3",children:[v.jsxs("div",{children:[v.jsx("p",{className:"text-sm text-macos-gray mb-1",children:"文件夹名称:"}),v.jsx("p",{className:"font-mono text-sm bg-white/50 rounded-macos px-3 py-2 border border-white/30",children:P})]}),v.jsxs("div",{children:[v.jsx("p",{className:"text-sm text-macos-gray mb-1",children:"完整路径:"}),v.jsxs("div",{className:"flex items-center space-x-2",children:[v.jsx("p",{className:"font-mono text-sm bg-white/50 rounded-macos px-3 py-2 border border-white/30 flex-1 truncate",children:L}),v.jsx("button",{onClick:ne,className:"w-8 h-8 bg-macos-blue/10 rounded-macos flex items-center justify-center text-macos-blue hover:bg-macos-blue/20 transition-colors",title:"复制路径",children:v.jsx(Iy,{className:"w-4 h-4"})})]})]})]})]}),v.jsxs("div",{className:"flex justify-center space-x-4 pt-4",children:[_==="completed"&&N>0&&v.jsxs(v.Fragment,{children:[v.jsxs("button",{onClick:o,className:"flex items-center space-x-3 px-8 py-4 bg-macos-green text-white rounded-macos-lg font-semibold shadow-macos hover:bg-macos-green/90 hover:shadow-macos-lg hover:scale-105 active:scale-95 transition-all duration-200",children:[v.jsx(By,{className:"w-5 h-5"}),v.jsx("span",{children:"下载压缩包"})]}),v.jsxs("button",{onClick:u,className:"flex items-center space-x-3 px-8 py-4 bg-macos-blue text-white rounded-macos-lg font-semibold shadow-macos hover:bg-macos-blue-hover hover:shadow-macos-lg hover:scale-105 active:scale-95 transition-all duration-200",children:[v.jsx(bl,{className:"w-5 h-5"}),v.jsx("span",{children:"打开文件夹"})]})]}),(_==="completed"||_==="failed")&&v.jsx("button",{onClick:a,className:"px-8 py-4 bg-macos-surface backdrop-blur-sm border border-white/30 text-gray-700 rounded-macos-lg font-semibold shadow-macos hover:bg-white/50 hover:shadow-macos-lg hover:scale-105 active:scale-95 transition-all duration-200",children:"重新开始"})]}),_==="completed"&&v.jsx("div",{className:"bg-macos-green/10 backdrop-blur-sm rounded-macos-lg p-6 border border-macos-green/20 shadow-macos",children:v.jsxs("div",{className:"flex items-start space-x-4",children:[v.jsx("div",{className:"w-10 h-10 bg-macos-green/20 rounded-macos-lg flex items-center justify-center flex-shrink-0",children:v.jsx(al,{className:"w-6 h-6 text-macos-green"})}),v.jsxs("div",{className:"text-macos-green",children:[v.jsx("p",{className:"font-bold text-lg mb-3",children:"转换完成！"}),v.jsxs("ul",{className:"space-y-2 text-sm leading-relaxed",children:[v.jsxs("li",{className:"flex items-start space-x-2",children:[v.jsx("span",{className:"w-1.5 h-1.5 bg-macos-green rounded-full mt-2 flex-shrink-0"}),v.jsxs("span",{children:["已生成 ",N," 个文件的Markdown和JSON格式"]})]}),v.jsxs("li",{className:"flex items-start space-x-2",children:[v.jsx("span",{className:"w-1.5 h-1.5 bg-macos-green rounded-full mt-2 flex-shrink-0"}),v.jsx("span",{children:"已创建合并后的完整文档"})]}),v.jsxs("li",{className:"flex items-start space-x-2",children:[v.jsx("span",{className:"w-1.5 h-1.5 bg-macos-green rounded-full mt-2 flex-shrink-0"}),v.jsx("span",{children:'点击"下载转换结果"获取所有文件的压缩包'})]})]})]})]})})]})})},Vn="http://localhost:3001";function Yy(){const[i,r]=ee.useState("upload"),[o,a]=ee.useState(null),[u,d]=ee.useState(null),[f,g]=ee.useState({current:0,total:0,currentFile:""}),[E,_]=ee.useState(null),[b,N]=ee.useState(!1);ee.useEffect(()=>{const Q=Wo(Vn);return _(Q),Q.on("conversionProgress",B=>{B.taskId===o&&(g({current:B.current,total:B.total,currentFile:B.currentFile}),d(U=>({...U,filesStatus:B.filesStatus||U.filesStatus,processingSpeed:B.processingSpeed||U.processingSpeed,estimatedTimeRemaining:B.estimatedTimeRemaining||U.estimatedTimeRemaining})))}),Q.on("conversionComplete",B=>{B.taskId===o&&(d(U=>({...U,status:"completed",completed:B.completed,failed:B.failed,errors:B.errors})),r("completed"))}),Q.on("conversionError",B=>{B.taskId===o&&(d(U=>({...U,status:"failed",error:B.error})),r("completed"))}),Q.on("conversionPaused",B=>{B.taskId===o&&d(U=>({...U,status:"paused"}))}),Q.on("conversionResumed",B=>{B.taskId===o&&d(U=>({...U,status:"processing"}))}),()=>{Q.close()}},[o]);const z=async Q=>{var B,U;try{N(!0);const ne=new FormData;Q.forEach(ze=>{ne.append("files",ze)});const ce=await Ee.post(`${Vn}/api/upload`,ne,{headers:{"Content-Type":"multipart/form-data"}}),ue=await Ee.post(`${Vn}/api/convert`,{files:ce.data.files,options:{}}),ke=ue.data.taskId;a(ke),d({id:ke,status:"processing",total:ue.data.total,completed:0,failed:0,errors:[],resultFolderName:ue.data.resultFolderName,resultFolderPath:ue.data.resultFolderPath}),r("converting")}catch(ne){console.error("转换启动失败:",ne),alert("转换启动失败: "+(((U=(B=ne.response)==null?void 0:B.data)==null?void 0:U.error)||ne.message))}finally{N(!1)}},J=async()=>{var Q,B;try{const U=await Ee.get(`${Vn}/api/download/${o}`,{responseType:"blob"}),ne=window.URL.createObjectURL(new Blob([U.data])),ce=document.createElement("a");ce.href=ne,ce.setAttribute("download",`${(u==null?void 0:u.resultFolderName)||"conversion-results"}.zip`),document.body.appendChild(ce),ce.click(),ce.remove(),window.URL.revokeObjectURL(ne)}catch(U){console.error("下载失败:",U),alert("下载失败: "+(((B=(Q=U.response)==null?void 0:Q.data)==null?void 0:B.error)||U.message))}},D=async()=>{var Q,B;try{const U=await Ee.post(`${Vn}/api/open-folder/${o}`);console.log("文件夹已打开:",U.data.folderPath)}catch(U){console.error("打开文件夹失败:",U),alert("打开文件夹失败: "+(((B=(Q=U.response)==null?void 0:Q.data)==null?void 0:B.error)||U.message))}},L=async()=>{var Q,B;try{await Ee.post(`${Vn}/api/pause/${o}`)}catch(U){console.error("暂停失败:",U),alert("暂停失败: "+(((B=(Q=U.response)==null?void 0:Q.data)==null?void 0:B.error)||U.message))}},P=async()=>{var Q,B;try{await Ee.post(`${Vn}/api/resume/${o}`)}catch(U){console.error("恢复失败:",U),alert("恢复失败: "+(((B=(Q=U.response)==null?void 0:Q.data)==null?void 0:B.error)||U.message))}},se=()=>{r("upload"),a(null),d(null),g({current:0,total:0,currentFile:""})};return v.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-macos-gray-light via-white to-macos-gray-light",children:[v.jsx("header",{className:"bg-macos-bg backdrop-blur-macos border-b border-white/20 shadow-macos",children:v.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:v.jsxs("div",{className:"flex items-center justify-between h-16",children:[v.jsxs("div",{className:"flex items-center space-x-3",children:[v.jsx("div",{className:"w-10 h-10 bg-macos-blue rounded-macos flex items-center justify-center shadow-macos",children:v.jsx(My,{className:"w-6 h-6 text-white"})}),v.jsx("h1",{className:"text-xl font-semibold text-gray-900 tracking-tight",children:"PDF批量转换工具"})]}),v.jsxs("div",{className:"flex items-center space-x-2 px-3 py-1.5 bg-macos-surface backdrop-blur-sm rounded-macos border border-white/30",children:[v.jsx($y,{className:"w-4 h-4 text-macos-gray"}),v.jsx("span",{className:"text-sm text-macos-gray font-medium",children:"Powered by Marker"})]})]})})}),v.jsxs("main",{className:"max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8",children:[i==="upload"&&v.jsxs("div",{className:"animate-macos-scale",children:[v.jsxs("div",{className:"text-center mb-12",children:[v.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-4 tracking-tight",children:"批量转换PDF到Markdown和JSON"}),v.jsx("p",{className:"text-lg text-macos-gray max-w-2xl mx-auto leading-relaxed",children:"上传您的PDF文件，系统将自动转换为Markdown和JSON格式"})]}),v.jsx("div",{className:"bg-macos-bg backdrop-blur-macos rounded-macos-xl shadow-macos-lg border border-white/30 p-8",children:v.jsx(Qy,{onFilesSelected:z,isUploading:b})})]}),(i==="converting"||i==="completed")&&v.jsxs("div",{className:"animate-macos-scale",children:[v.jsxs("div",{className:"text-center mb-12",children:[v.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-4 tracking-tight",children:"转换进度"}),v.jsx("p",{className:"text-lg text-macos-gray max-w-2xl mx-auto leading-relaxed",children:"正在处理您的PDF文件，请耐心等待..."})]}),v.jsx("div",{className:"bg-macos-bg backdrop-blur-macos rounded-macos-xl shadow-macos-lg border border-white/30 p-8",children:v.jsx(Jy,{taskStatus:{...u,currentFile:f.currentFile},progress:f,onDownload:J,onOpenFolder:D,onPause:L,onResume:P,onReset:se})})]})]}),v.jsx("footer",{className:"bg-macos-bg backdrop-blur-macos border-t border-white/20 mt-20",children:v.jsx("div",{className:"max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8",children:v.jsx("p",{className:"text-center text-sm text-macos-gray font-medium",children:"© 2025 PDF批量转换工具. 基于Marker引擎构建."})})})]})}ph.createRoot(document.getElementById("root")).render(v.jsx(ee.StrictMode,{children:v.jsx(Yy,{})}));
