import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { io } from 'socket.io-client';
import FileUploader from './components/FileUploader';
import ConversionProgress from './components/ConversionProgress';
import { FileText, Settings } from 'lucide-react';

const API_BASE_URL = 'http://localhost:3001';

function App() {
  const [currentStep, setCurrentStep] = useState('upload'); // 'upload', 'converting', 'completed'
  const [taskId, setTaskId] = useState(null);
  const [taskStatus, setTaskStatus] = useState(null);
  const [progress, setProgress] = useState({ current: 0, total: 0, currentFile: '' });
  const [socket, setSocket] = useState(null);
  const [isUploading, setIsUploading] = useState(false);

  // 初始化Socket.IO连接
  useEffect(() => {
    const newSocket = io(API_BASE_URL);
    setSocket(newSocket);

    // 监听转换进度
    newSocket.on('conversionProgress', (data) => {
      if (data.taskId === taskId) {
        setProgress({
          current: data.current,
          total: data.total,
          currentFile: data.currentFile
        });

        // 更新任务状态中的详细信息
        setTaskStatus(prev => ({
          ...prev,
          filesStatus: data.filesStatus || prev.filesStatus,
          processingSpeed: data.processingSpeed || prev.processingSpeed,
          estimatedTimeRemaining: data.estimatedTimeRemaining || prev.estimatedTimeRemaining
        }));
      }
    });

    // 监听转换完成
    newSocket.on('conversionComplete', (data) => {
      if (data.taskId === taskId) {
        setTaskStatus(prev => ({
          ...prev,
          status: 'completed',
          completed: data.completed,
          failed: data.failed,
          errors: data.errors
        }));
        setCurrentStep('completed');
      }
    });

    // 监听转换错误
    newSocket.on('conversionError', (data) => {
      if (data.taskId === taskId) {
        setTaskStatus(prev => ({
          ...prev,
          status: 'failed',
          error: data.error
        }));
        setCurrentStep('completed');
      }
    });

    // 监听暂停事件
    newSocket.on('conversionPaused', (data) => {
      if (data.taskId === taskId) {
        setTaskStatus(prev => ({
          ...prev,
          status: 'paused'
        }));
      }
    });

    // 监听恢复事件
    newSocket.on('conversionResumed', (data) => {
      if (data.taskId === taskId) {
        setTaskStatus(prev => ({
          ...prev,
          status: 'processing'
        }));
      }
    });

    return () => {
      newSocket.close();
    };
  }, [taskId]);

  // 处理文件上传和转换
  const handleFilesSelected = async (files) => {
    try {
      setIsUploading(true);
      
      // 创建FormData上传文件
      const formData = new FormData();
      files.forEach(file => {
        formData.append('files', file);
      });

      // 上传文件
      const uploadResponse = await axios.post(`${API_BASE_URL}/api/upload`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      // 开始转换
      const convertResponse = await axios.post(`${API_BASE_URL}/api/convert`, {
        files: uploadResponse.data.files,
        options: {}
      });

      const newTaskId = convertResponse.data.taskId;
      setTaskId(newTaskId);
      setTaskStatus({
        id: newTaskId,
        status: 'processing',
        total: convertResponse.data.total,
        completed: 0,
        failed: 0,
        errors: [],
        resultFolderName: convertResponse.data.resultFolderName,
        resultFolderPath: convertResponse.data.resultFolderPath
      });
      setCurrentStep('converting');
      
    } catch (error) {
      console.error('转换启动失败:', error);
      alert('转换启动失败: ' + (error.response?.data?.error || error.message));
    } finally {
      setIsUploading(false);
    }
  };

  // 下载转换结果
  const handleDownload = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/download/${taskId}`, {
        responseType: 'blob'
      });

      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `${taskStatus?.resultFolderName || 'conversion-results'}.zip`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('下载失败:', error);
      alert('下载失败: ' + (error.response?.data?.error || error.message));
    }
  };

  // 打开结果文件夹
  const handleOpenFolder = async () => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/open-folder/${taskId}`);
      console.log('文件夹已打开:', response.data.folderPath);
    } catch (error) {
      console.error('打开文件夹失败:', error);
      alert('打开文件夹失败: ' + (error.response?.data?.error || error.message));
    }
  };

  // 暂停转换
  const handlePause = async () => {
    try {
      await axios.post(`${API_BASE_URL}/api/pause/${taskId}`);
    } catch (error) {
      console.error('暂停失败:', error);
      alert('暂停失败: ' + (error.response?.data?.error || error.message));
    }
  };

  // 恢复转换
  const handleResume = async () => {
    try {
      await axios.post(`${API_BASE_URL}/api/resume/${taskId}`);
    } catch (error) {
      console.error('恢复失败:', error);
      alert('恢复失败: ' + (error.response?.data?.error || error.message));
    }
  };

  // 重置应用状态
  const handleReset = () => {
    setCurrentStep('upload');
    setTaskId(null);
    setTaskStatus(null);
    setProgress({ current: 0, total: 0, currentFile: '' });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-macos-gray-light via-white to-macos-gray-light">
      {/* 头部 */}
      <header className="bg-macos-bg backdrop-blur-macos border-b border-white/20 shadow-macos">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-macos-blue rounded-macos flex items-center justify-center shadow-macos">
                <FileText className="w-6 h-6 text-white" />
              </div>
              <h1 className="text-xl font-semibold text-gray-900 tracking-tight">
                PDF批量转换工具
              </h1>
            </div>
            <div className="flex items-center space-x-2 px-3 py-1.5 bg-macos-surface backdrop-blur-sm rounded-macos border border-white/30">
              <Settings className="w-4 h-4 text-macos-gray" />
              <span className="text-sm text-macos-gray font-medium">Powered by Marker</span>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        {currentStep === 'upload' && (
          <div className="animate-macos-scale">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4 tracking-tight">
                批量转换PDF到Markdown和JSON
              </h2>
              <p className="text-lg text-macos-gray max-w-2xl mx-auto leading-relaxed">
                上传您的PDF文件，系统将自动转换为Markdown和JSON格式
              </p>
            </div>
            <div className="bg-macos-bg backdrop-blur-macos rounded-macos-xl shadow-macos-lg border border-white/30 p-8">
              <FileUploader
                onFilesSelected={handleFilesSelected}
                isUploading={isUploading}
              />
            </div>
          </div>
        )}

        {(currentStep === 'converting' || currentStep === 'completed') && (
          <div className="animate-macos-scale">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4 tracking-tight">
                转换进度
              </h2>
              <p className="text-lg text-macos-gray max-w-2xl mx-auto leading-relaxed">
                正在处理您的PDF文件，请耐心等待...
              </p>
            </div>
            <div className="bg-macos-bg backdrop-blur-macos rounded-macos-xl shadow-macos-lg border border-white/30 p-8">
              <ConversionProgress
                taskStatus={{
                  ...taskStatus,
                  currentFile: progress.currentFile
                }}
                progress={progress}
                onDownload={handleDownload}
                onOpenFolder={handleOpenFolder}
                onPause={handlePause}
                onResume={handleResume}
                onReset={handleReset}
              />
            </div>
          </div>
        )}
      </main>

      {/* 页脚 */}
      <footer className="bg-macos-bg backdrop-blur-macos border-t border-white/20 mt-20">
        <div className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
          <p className="text-center text-sm text-macos-gray font-medium">
            © 2025 PDF批量转换工具. 基于Marker引擎构建.
          </p>
        </div>
      </footer>
    </div>
  );
}

export default App;
